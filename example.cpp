#include "mongoose_cpp.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace mongoose_cpp;

// HTTP Server Example
void http_server_example() {
    std::cout << "=== HTTP Server Example ===" << std::endl;
    
    try {
        Manager manager;
        http::Server server(manager);
        
        // Setup routes
        server.get("/", [](const http::Message& req, http::Response& res) {
            res.html("<h1>Welcome to Mongoose C++!</h1>"
                    "<p><a href='/api/hello'>API Example</a></p>"
                    "<p><a href='/json'>JSON Example</a></p>");
        });
        
        server.get("/api/hello", [](const http::Message& req, http::Response& res) {
            std::string name = req.get_query_param("name");
            if (name.empty()) name = "World";
            res.json("{\"message\": \"Hello, " + name + "!\"}");
        });
        
        server.post("/api/echo", [](const http::Message& req, http::Response& res) {
            res.json("{\"echo\": \"" + req.body().to_string() + "\"}");
        });
        
        server.get("/json", [](const http::Message& req, http::Response& res) {
            res.json("{\"status\": \"ok\", \"timestamp\": " + 
                    std::to_string(std::time(nullptr)) + "}");
        });
        
        // Enable static file serving
        server.static_files("./www");
        
        // Start server
        server.listen("http://0.0.0.0:8080");
        std::cout << "HTTP Server listening on http://localhost:8080" << std::endl;
        
        // Start polling in background
        manager.start_polling_thread();
        
        // Run for 30 seconds
        std::this_thread::sleep_for(std::chrono::seconds(30));
        
        manager.stop_polling_thread();
        std::cout << "HTTP Server stopped" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "HTTP Server error: " << e.what() << std::endl;
    }
}

// WebSocket Server Example
void websocket_server_example() {
    std::cout << "\n=== WebSocket Server Example ===" << std::endl;
    
    try {
        Manager manager;
        websocket::Server server(manager);
        
        server.on_connect([](websocket::WebSocketConnection& conn) {
            std::cout << "WebSocket client connected: " << conn.get_id() << std::endl;
            conn.send_text("Welcome to WebSocket server!");
        });
        
        server.on_message([&server](websocket::WebSocketConnection& conn, const websocket::Message& msg) {
            std::cout << "Received: " << msg.to_string() << std::endl;
            
            if (msg.is_text()) {
                std::string text = msg.to_string();
                if (text == "ping") {
                    conn.send_text("pong");
                } else if (text == "broadcast") {
                    server.broadcast_text("Broadcast message from server!");
                } else {
                    conn.send_text("Echo: " + text);
                }
            }
        });
        
        server.on_close([](websocket::WebSocketConnection& conn) {
            std::cout << "WebSocket client disconnected: " << conn.get_id() << std::endl;
        });
        
        server.listen("ws://0.0.0.0:8081", "/ws");
        std::cout << "WebSocket Server listening on ws://localhost:8081/ws" << std::endl;
        
        manager.start_polling_thread();
        std::this_thread::sleep_for(std::chrono::seconds(30));
        manager.stop_polling_thread();
        
        std::cout << "WebSocket Server stopped" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "WebSocket Server error: " << e.what() << std::endl;
    }
}

// MQTT Client Example
void mqtt_client_example() {
    std::cout << "\n=== MQTT Client Example ===" << std::endl;
    
    try {
        Manager manager;
        mqtt::Client client(manager);
        
        client.on_connect([]() {
            std::cout << "MQTT connected!" << std::endl;
        });
        
        client.on_message([](const mqtt::Message& msg) {
            std::cout << "MQTT message received on topic '" << msg.topic_string() 
                     << "': " << msg.data_string() << std::endl;
        });
        
        client.on_disconnect([]() {
            std::cout << "MQTT disconnected!" << std::endl;
        });
        
        // Configure MQTT options
        mqtt::Options options;
        options.client_id = "mongoose_cpp_example";
        options.keepalive = 60;
        options.clean = true;
        
        // Connect to MQTT broker (you need a running MQTT broker)
        // client.connect("mqtt://localhost:1883", options);
        std::cout << "MQTT example skipped (no broker configured)" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "MQTT Client error: " << e.what() << std::endl;
    }
}

// File Server Example
void file_server_example() {
    std::cout << "\n=== File Server Example ===" << std::endl;
    
    try {
        Manager manager;
        file::FileServer server(manager);
        
        file::ServeOptions options;
        options.root_dir = "./www";
        options.extra_headers = "Access-Control-Allow-Origin: *\r\n";
        
        server.listen("http://0.0.0.0:8082", options);
        std::cout << "File Server listening on http://localhost:8082" << std::endl;
        
        manager.start_polling_thread();
        std::this_thread::sleep_for(std::chrono::seconds(30));
        manager.stop_polling_thread();
        
        std::cout << "File Server stopped" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "File Server error: " << e.what() << std::endl;
    }
}

// Timer Example
void timer_example() {
    std::cout << "\n=== Timer Example ===" << std::endl;
    
    try {
        Manager manager;
        
        int counter = 0;
        auto timer = manager.add_timer(std::chrono::milliseconds(1000), [&counter]() {
            std::cout << "Timer tick: " << ++counter << std::endl;
        }, true); // repeat = true
        
        manager.start_polling_thread();
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        timer->cancel();
        std::cout << "Timer cancelled" << std::endl;
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        manager.stop_polling_thread();
        
    } catch (const std::exception& e) {
        std::cerr << "Timer error: " << e.what() << std::endl;
    }
}

int main() {
    std::cout << "Mongoose C++ Wrapper Examples" << std::endl;
    std::cout << "=============================" << std::endl;
    
    // Run examples one by one
    http_server_example();
    websocket_server_example();
    mqtt_client_example();
    file_server_example();
    timer_example();
    
    std::cout << "\nAll examples completed!" << std::endl;
    return 0;
}
