#!/bin/bash

# Mongoose C++ Build Script
set -e

echo "Building Mongoose C++ Wrapper..."

# Create build directory
mkdir -p build
cd build

# Configure with CMake
echo "Configuring with CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build
echo "Building..."
cmake --build . --parallel $(nproc)

echo "Build completed successfully!"

# Run tests if available
if [ -f "./test_mongoose_cpp" ]; then
    echo "Running tests..."
    ./test_mongoose_cpp
fi

echo "Build script finished!"
