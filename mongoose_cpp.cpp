#include "mongoose_cpp.hpp"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <cctype>

namespace mongoose_cpp {

// Address implementation
std::string Address::to_string() const {
    char buf[64];
    if (addr_.is_ip6) {
        snprintf(buf, sizeof(buf), "[%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x]:%d",
                 addr_.ip[0], addr_.ip[1], addr_.ip[2], addr_.ip[3],
                 addr_.ip[4], addr_.ip[5], addr_.ip[6], addr_.ip[7],
                 addr_.ip[8], addr_.ip[9], addr_.ip[10], addr_.ip[11],
                 addr_.ip[12], addr_.ip[13], addr_.ip[14], addr_.ip[15],
                 ntohs(addr_.port));
    } else {
        uint32_t ip = (addr_.ip[0] << 24) | (addr_.ip[1] << 16) | (addr_.ip[2] << 8) | addr_.ip[3];
        snprintf(buf, sizeof(buf), "%d.%d.%d.%d:%d",
                 (ip >> 24) & 0xff, (ip >> 16) & 0xff,
                 (ip >> 8) & 0xff, ip & 0xff,
                 ntohs(addr_.port));
    }
    return std::string(buf);
}

uint16_t Address::port() const {
    return ntohs(addr_.port);
}

// Timer implementation
Timer::Timer(Manager* mgr, Duration interval, TimerCallback callback, bool repeat)
    : timer_(nullptr), id_(0), callback_(callback), manager_(mgr), repeat_(repeat) {
    if (!mgr) {
        throw MongooseException("Manager cannot be null");
    }
    
    timer_ = mg_timer_add(mgr->get_mgr(), interval.count(), 
                         repeat ? MG_TIMER_REPEAT : 0, timer_callback, this);
    if (!timer_) {
        throw MongooseException("Failed to create timer");
    }
    
    id_ = reinterpret_cast<unsigned long>(timer_);
    mgr->register_timer(id_, std::shared_ptr<Timer>(this));
}

Timer::~Timer() {
    cleanup();
}

Timer::Timer(Timer&& other) noexcept {
    move_from(std::move(other));
}

Timer& Timer::operator=(Timer&& other) noexcept {
    if (this != &other) {
        cleanup();
        move_from(std::move(other));
    }
    return *this;
}

void Timer::cancel() {
    if (timer_ && manager_) {
        manager_->unregister_timer_internal(id_);
        timer_ = nullptr;
        manager_ = nullptr;
    }
}

void Timer::cleanup() {
    if (timer_ && manager_) {
        manager_->unregister_timer_internal(id_);
    }
    timer_ = nullptr;
    manager_ = nullptr;
    id_ = 0;
}

void Timer::move_from(Timer&& other) {
    timer_ = other.timer_;
    id_ = other.id_;
    callback_ = std::move(other.callback_);
    manager_ = other.manager_;
    repeat_ = other.repeat_;
    
    other.timer_ = nullptr;
    other.id_ = 0;
    other.manager_ = nullptr;
    other.repeat_ = false;
}

void Timer::timer_callback(void* arg) {
    Timer* timer = static_cast<Timer*>(arg);
    if (timer && timer->callback_) {
        timer->callback_();
    }
}

// Connection implementation
Connection::Connection(struct mg_connection* conn, Manager* mgr, EventHandler handler)
    : conn_(conn), manager_(mgr), event_handler_(handler), valid_(true) {
    if (conn_) {
        conn_->fn = Manager::internal_event_handler;
        conn_->fn_data = this;
    }
}

Connection::~Connection() {
    invalidate();
}

std::shared_ptr<Connection> Connection::create(struct mg_connection* conn, Manager* mgr, EventHandler handler) {
    if (!conn || !mgr) {
        return nullptr;
    }
    
    // Use shared_ptr constructor that allows private constructor access
    auto connection = std::shared_ptr<Connection>(new Connection(conn, mgr, handler));
    mgr->register_connection(conn->id, connection);
    return connection;
}

size_t Connection::printf(const char* fmt, ...) {
    if (!conn_) return 0;
    va_list ap;
    va_start(ap, fmt);
    size_t result = mg_vprintf(conn_, fmt, &ap);
    va_end(ap);
    return result;
}

size_t Connection::vprintf(const char* fmt, va_list ap) {
    return conn_ ? mg_vprintf(conn_, fmt, &ap) : 0;
}

Buffer Connection::get_recv_buffer() const {
    if (!conn_) return Buffer();
    return Buffer(conn_->recv.buf, conn_->recv.len);
}

Buffer Connection::get_send_buffer() const {
    if (!conn_) return Buffer();
    return Buffer(conn_->send.buf, conn_->send.len);
}

void Connection::clear_recv_buffer() {
    if (conn_) {
        conn_->recv.len = 0;
    }
}

void Connection::clear_send_buffer() {
    if (conn_) {
        conn_->send.len = 0;
    }
}

void Connection::close() {
    if (conn_) {
        conn_->is_draining = 1;
        invalidate();
    }
}

void Connection::dispatch_event(int event, void* event_data) {
    if (event_handler_ && valid_.load()) {
        try {
            event_handler_(*this, event, event_data);
        } catch (const std::exception& e) {
            std::cerr << "Exception in event handler: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Unknown exception in event handler" << std::endl;
        }
    }
}

void Connection::invalidate() {
    valid_.store(false);
    if (conn_ && manager_) {
        manager_->unregister_connection(conn_->id);
        conn_ = nullptr;
    }
}

// Manager implementation
Manager::Manager() : running_(false) {
    mg_mgr_init(&mgr_);
}

Manager::~Manager() {
    cleanup();
}

Manager::Manager(Manager&& other) noexcept {
    move_from(std::move(other));
}

Manager& Manager::operator=(Manager&& other) noexcept {
    if (this != &other) {
        cleanup();
        move_from(std::move(other));
    }
    return *this;
}

void Manager::poll(int timeout_ms) {
    mg_mgr_poll(&mgr_, timeout_ms);
}

void Manager::start_polling_thread() {
    if (running_.load()) {
        return;
    }
    
    running_.store(true);
    poll_thread_ = std::make_unique<std::thread>([this]() {
        while (running_.load()) {
            poll(1000);
        }
    });
}

void Manager::stop_polling_thread() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    if (poll_thread_ && poll_thread_->joinable()) {
        poll_thread_->join();
    }
    poll_thread_.reset();
}

std::shared_ptr<Connection> Manager::listen(const std::string& url, EventHandler handler) {
    struct mg_connection* conn = mg_listen(&mgr_, url.c_str(), internal_event_handler, nullptr);
    if (!conn) {
        throw NetworkException("Failed to listen on " + url);
    }
    
    return Connection::create(conn, this, handler);
}

std::shared_ptr<Connection> Manager::connect(const std::string& url, EventHandler handler) {
    struct mg_connection* conn = mg_connect(&mgr_, url.c_str(), internal_event_handler, nullptr);
    if (!conn) {
        throw NetworkException("Failed to connect to " + url);
    }
    
    return Connection::create(conn, this, handler);
}

std::shared_ptr<Connection> Manager::wrap_fd(int fd, EventHandler handler) {
    struct mg_connection* conn = mg_wrapfd(&mgr_, fd, internal_event_handler, nullptr);
    if (!conn) {
        throw NetworkException("Failed to wrap file descriptor " + std::to_string(fd));
    }
    
    return Connection::create(conn, this, handler);
}

std::shared_ptr<Timer> Manager::add_timer(Duration interval, TimerCallback callback, bool repeat) {
    return std::make_shared<Timer>(this, interval, callback, repeat);
}

void Manager::remove_timer(unsigned long timer_id) {
    std::lock_guard<std::mutex> lock(timers_mutex_);
    auto it = timers_.find(timer_id);
    if (it != timers_.end()) {
        it->second->cancel();
        timers_.erase(it);
    }
}

bool Manager::wakeup(unsigned long connection_id, const void* data, size_t len) {
    return mg_wakeup(&mgr_, connection_id, data, len);
}

bool Manager::init_wakeup() {
    return mg_wakeup_init(&mgr_);
}

void Manager::register_connection(unsigned long id, std::shared_ptr<Connection> conn) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    connections_[id] = conn;
}

void Manager::unregister_connection(unsigned long id) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    connections_.erase(id);
}

std::shared_ptr<Connection> Manager::get_connection(unsigned long id) const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    auto it = connections_.find(id);
    return (it != connections_.end()) ? it->second : nullptr;
}

void Manager::register_timer(unsigned long id, std::shared_ptr<Timer> timer) {
    std::lock_guard<std::mutex> lock(timers_mutex_);
    timers_[id] = timer;
}

void Manager::unregister_timer_internal(unsigned long id) {
    std::lock_guard<std::mutex> lock(timers_mutex_);
    timers_.erase(id);
}

void Manager::internal_event_handler(struct mg_connection* c, int ev, void* ev_data) {
    if (!c || !c->fn_data) return;
    
    Connection* conn = static_cast<Connection*>(c->fn_data);
    if (conn) {
        conn->dispatch_event(ev, ev_data);
    }
}

void Manager::cleanup() {
    stop_polling_thread();
    
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        for (auto& pair : connections_) {
            if (pair.second) {
                pair.second->invalidate();
            }
        }
        connections_.clear();
    }

    {
        std::lock_guard<std::mutex> lock(timers_mutex_);
        for (auto& pair : timers_) {
            if (pair.second) {
                pair.second->cancel();
            }
        }
        timers_.clear();
    }
    
    mg_mgr_free(&mgr_);
}

void Manager::move_from(Manager&& other) {
    mgr_ = other.mgr_;
    running_.store(other.running_.load());
    poll_thread_ = std::move(other.poll_thread_);
    connections_ = std::move(other.connections_);
    timers_ = std::move(other.timers_);
    
    // Reset other
    memset(&other.mgr_, 0, sizeof(other.mgr_));
    other.running_.store(false);
    other.connections_.clear();
    other.timers_.clear();
}

// HTTP implementation
namespace http {

Message::Message(struct mg_http_message* msg) : msg_(msg), headers_parsed_(false) {}

void Message::parse_headers() {
    if (headers_parsed_ || !msg_) return;

    headers_.clear();
    for (int i = 0; i < MG_MAX_HTTP_HEADERS && msg_->headers[i].name.len > 0; i++) {
        headers_.emplace_back(msg_->headers[i]);
    }
    headers_parsed_ = true;
}

const std::vector<Header>& Message::headers() {
    parse_headers();
    return headers_;
}

std::string Message::get_header(const std::string& name) const {
    if (!msg_) return "";

    struct mg_str* header = mg_http_get_header(msg_, name.c_str());
    if (header) {
        return std::string(header->buf, header->len);
    }
    return "";
}

bool Message::has_header(const std::string& name) const {
    return !get_header(name).empty();
}

std::string Message::get_query_param(const std::string& name) const {
    if (!msg_) return "";

    char buf[256];
    int len = mg_http_get_var(&msg_->query, name.c_str(), buf, sizeof(buf));
    return (len > 0) ? std::string(buf, len) : std::string();
}

std::map<std::string, std::string> Message::get_query_params() const {
    std::map<std::string, std::string> params;
    if (!msg_) return params;

    std::string query_str(msg_->query.buf, msg_->query.len);
    std::istringstream iss(query_str);
    std::string pair;

    while (std::getline(iss, pair, '&')) {
        size_t eq_pos = pair.find('=');
        if (eq_pos != std::string::npos) {
            std::string key = url_decode(pair.substr(0, eq_pos));
            std::string value = url_decode(pair.substr(eq_pos + 1));
            params[key] = value;
        }
    }

    return params;
}

std::string Message::get_form_param(const std::string& name) const {
    if (!msg_) return "";

    char buf[256];
    int len = mg_http_get_var(&msg_->body, name.c_str(), buf, sizeof(buf));
    return (len > 0) ? std::string(buf, len) : std::string();
}

std::map<std::string, std::string> Message::get_form_params() const {
    std::map<std::string, std::string> params;
    if (!msg_) return params;

    std::string body_str(msg_->body.buf, msg_->body.len);
    std::istringstream iss(body_str);
    std::string pair;

    while (std::getline(iss, pair, '&')) {
        size_t eq_pos = pair.find('=');
        if (eq_pos != std::string::npos) {
            std::string key = url_decode(pair.substr(0, eq_pos));
            std::string value = url_decode(pair.substr(eq_pos + 1));
            params[key] = value;
        }
    }

    return params;
}

Method Message::get_method() const {
    if (!msg_) return Method::UNKNOWN;
    return string_to_method(std::string(msg_->method.buf, msg_->method.len));
}

std::string Message::get_path() const {
    if (!msg_) return "";

    std::string uri_str(msg_->uri.buf, msg_->uri.len);
    size_t query_pos = uri_str.find('?');
    return (query_pos != std::string::npos) ? uri_str.substr(0, query_pos) : uri_str;
}

bool Message::is_websocket_upgrade() const {
    std::string connection = get_header("Connection");
    std::string upgrade = get_header("Upgrade");

    std::transform(connection.begin(), connection.end(), connection.begin(), ::tolower);
    std::transform(upgrade.begin(), upgrade.end(), upgrade.begin(), ::tolower);

    return connection.find("upgrade") != std::string::npos && upgrade == "websocket";
}

std::pair<std::string, std::string> Message::get_basic_auth() const {
    if (!msg_) return {};

    char user[256], pass[256];
    mg_http_creds(msg_, user, sizeof(user), pass, sizeof(pass));
    return {std::string(user), std::string(pass)};
}

std::string Message::get_content_type() const {
    return get_header("Content-Type");
}

size_t Message::get_content_length() const {
    std::string cl = get_header("Content-Length");
    return cl.empty() ? 0 : std::stoul(cl);
}

// Response implementation
Response::~Response() {
    if (!sent_) {
        try {
            send();
        } catch (...) {
            // Ignore exceptions in destructor
        }
    }
}

Response::Response(Response&& other) noexcept
    : conn_(other.conn_), status_(other.status_),
      headers_(std::move(other.headers_)), body_(std::move(other.body_)), sent_(other.sent_) {
    other.sent_ = true; // Prevent other from sending
}

Response& Response::operator=(Response&& other) noexcept {
    if (this != &other) {
        if (!sent_) {
            try {
                send();
            } catch (...) {
                // Ignore exceptions
            }
        }

        conn_ = other.conn_;
        status_ = other.status_;
        headers_ = std::move(other.headers_);
        body_ = std::move(other.body_);
        sent_ = other.sent_;
        other.sent_ = true;
    }
    return *this;
}

Response& Response::header(const std::string& name, const std::string& value) {
    ensure_not_sent();
    headers_[name] = value;
    return *this;
}

Response& Response::content_type(const std::string& type) {
    return header("Content-Type", type);
}

Response& Response::content_length(size_t length) {
    return header("Content-Length", std::to_string(length));
}

Response& Response::json(const std::string& json) {
    return content_type("application/json").body(json);
}

Response& Response::html(const std::string& html) {
    return content_type("text/html").body(html);
}

Response& Response::text(const std::string& text) {
    return content_type("text/plain").body(text);
}

void Response::send() {
    ensure_not_sent();

    std::string headers_str = build_headers();
    mg_http_reply(conn_.get_mg_connection(), static_cast<int>(status_),
                  headers_str.c_str(), "%s", body_.c_str());
    sent_ = true;
}

void Response::send(const std::string& body) {
    body_ = body;
    send();
}

void Response::send(Status status, const std::string& body) {
    status_ = status;
    body_ = body;
    send();
}

void Response::send_chunk(const std::string& chunk) {
    ensure_not_sent();
    mg_http_write_chunk(conn_.get_mg_connection(), chunk.c_str(), chunk.size());
}

void Response::end_chunks() {
    ensure_not_sent();
    mg_http_write_chunk(conn_.get_mg_connection(), "", 0);
    sent_ = true;
}

void Response::send_file(const std::string& path) {
    ensure_not_sent();

    struct mg_http_serve_opts opts = {};
    mg_http_serve_file(conn_.get_mg_connection(), nullptr, path.c_str(), &opts);
    sent_ = true;
}

void Response::send_file(const std::string& path, const std::string& content_type) {
    this->content_type(content_type);
    send_file(path);
}

void Response::redirect(const std::string& url, Status status) {
    ensure_not_sent();
    header("Location", url);
    status_ = status;
    send();
}

void Response::error(Status status, const std::string& message) {
    ensure_not_sent();
    status_ = status;
    if (message.empty()) {
        body_ = status_to_reason_phrase(status);
    } else {
        body_ = message;
    }
    content_type("text/plain");
    send();
}

void Response::ensure_not_sent() {
    if (sent_) {
        throw MongooseException("Response already sent");
    }
}

std::string Response::build_headers() {
    std::ostringstream oss;
    for (const auto& pair : headers_) {
        oss << pair.first << ": " << pair.second << "\r\n";
    }
    return oss.str();
}

// Route implementation
Route::Route(Method method, const std::string& pattern, RouteHandler handler)
    : pattern_(pattern), handler_(handler), method_(method) {
    regex_ = compile_pattern(pattern);
}

bool Route::matches(Method method, const std::string& path) const {
    if (method != method_) return false;
    return std::regex_match(path, regex_);
}

void Route::handle(const Message& request, Response& response) const {
    if (handler_) {
        handler_(request, response);
    }
}

std::regex Route::compile_pattern(const std::string& pattern) {
    std::string regex_pattern = pattern;

    // Convert simple patterns to regex
    // Replace :param with ([^/]+)
    size_t pos = 0;
    while ((pos = regex_pattern.find(':', pos)) != std::string::npos) {
        size_t end = regex_pattern.find('/', pos);
        if (end == std::string::npos) end = regex_pattern.length();

        regex_pattern.replace(pos, end - pos, "([^/]+)");
        pos += 7; // Length of "([^/]+)"
    }

    // Replace * with .*
    pos = 0;
    while ((pos = regex_pattern.find('*', pos)) != std::string::npos) {
        regex_pattern.replace(pos, 1, ".*");
        pos += 2;
    }

    return std::regex(regex_pattern);
}

// Server implementation
Server::Server(Manager& manager) : manager_(manager), serve_static_(false) {}

void Server::listen(const std::string& address) {
    listener_ = manager_.listen(address,
        [this](Connection& conn, int event, void* event_data) {
            http_event_handler(conn, event, event_data);
        });

    if (listener_) {
        listener_->set_user_data(this);
    }
}

void Server::stop() {
    if (listener_) {
        listener_->close();
        listener_.reset();
    }
}

bool Server::is_listening() const {
    return listener_ && listener_->is_valid() && listener_->is_listening();
}

Server& Server::get(const std::string& pattern, RouteHandler handler) {
    return route(Method::GET, pattern, handler);
}

Server& Server::post(const std::string& pattern, RouteHandler handler) {
    return route(Method::POST, pattern, handler);
}

Server& Server::put(const std::string& pattern, RouteHandler handler) {
    return route(Method::PUT, pattern, handler);
}

Server& Server::delete_(const std::string& pattern, RouteHandler handler) {
    return route(Method::DELETE, pattern, handler);
}

Server& Server::head(const std::string& pattern, RouteHandler handler) {
    return route(Method::HEAD, pattern, handler);
}

Server& Server::options(const std::string& pattern, RouteHandler handler) {
    return route(Method::OPTIONS, pattern, handler);
}

Server& Server::patch(const std::string& pattern, RouteHandler handler) {
    return route(Method::PATCH, pattern, handler);
}

Server& Server::route(Method method, const std::string& pattern, RouteHandler handler) {
    routes_.emplace_back(method, pattern, handler);
    return *this;
}

Server& Server::static_files(const std::string& document_root) {
    document_root_ = document_root;
    serve_static_ = true;
    return *this;
}

Server& Server::disable_static_files() {
    serve_static_ = false;
    return *this;
}

void Server::http_event_handler(Connection& conn, int event, void* event_data) {
    if (event == MG_EV_HTTP_MSG) {
        struct mg_http_message* msg = static_cast<struct mg_http_message*>(event_data);
        static_cast<Server*>(conn.get_user_data())->handle_http_message(conn, msg);
    }
}

void Server::handle_http_message(Connection& conn, struct mg_http_message* msg) {
    Message request(msg);
    Response response(conn);

    // Try to handle with routes first
    if (handle_route(request, response)) {
        return;
    }

    // Fall back to static file serving
    if (serve_static_) {
        handle_static_file(request, response);
    } else {
        response.error(Status::NOT_FOUND);
    }
}

bool Server::handle_route(const Message& request, Response& response) {
    Method method = request.get_method();
    std::string path = request.get_path();

    for (const auto& route : routes_) {
        if (route.matches(method, path)) {
            route.handle(request, response);
            return true;
        }
    }

    return false;
}

void Server::handle_static_file(const Message& request, Response& response) {
    if (document_root_.empty()) {
        response.error(Status::NOT_FOUND);
        return;
    }

    struct mg_http_serve_opts opts = {};
    opts.root_dir = document_root_.c_str();

    mg_http_serve_dir(response.conn_.get_mg_connection(),
                      request.get_mg_message_mutable(), &opts);
}

// Utility functions
std::string method_to_string(Method method) {
    switch (method) {
        case Method::GET: return "GET";
        case Method::POST: return "POST";
        case Method::PUT: return "PUT";
        case Method::DELETE: return "DELETE";
        case Method::HEAD: return "HEAD";
        case Method::OPTIONS: return "OPTIONS";
        case Method::PATCH: return "PATCH";
        case Method::CONNECT: return "CONNECT";
        case Method::TRACE: return "TRACE";
        default: return "UNKNOWN";
    }
}

Method string_to_method(const std::string& method) {
    if (method == "GET") return Method::GET;
    if (method == "POST") return Method::POST;
    if (method == "PUT") return Method::PUT;
    if (method == "DELETE") return Method::DELETE;
    if (method == "HEAD") return Method::HEAD;
    if (method == "OPTIONS") return Method::OPTIONS;
    if (method == "PATCH") return Method::PATCH;
    if (method == "CONNECT") return Method::CONNECT;
    if (method == "TRACE") return Method::TRACE;
    return Method::UNKNOWN;
}

std::string status_to_string(Status status) {
    return std::to_string(static_cast<int>(status));
}

const char* status_to_reason_phrase(Status status) {
    switch (status) {
        case Status::OK: return "OK";
        case Status::CREATED: return "Created";
        case Status::ACCEPTED: return "Accepted";
        case Status::NO_CONTENT: return "No Content";
        case Status::MOVED_PERMANENTLY: return "Moved Permanently";
        case Status::FOUND: return "Found";
        case Status::NOT_MODIFIED: return "Not Modified";
        case Status::BAD_REQUEST: return "Bad Request";
        case Status::UNAUTHORIZED: return "Unauthorized";
        case Status::FORBIDDEN: return "Forbidden";
        case Status::NOT_FOUND: return "Not Found";
        case Status::METHOD_NOT_ALLOWED: return "Method Not Allowed";
        case Status::CONFLICT: return "Conflict";
        case Status::INTERNAL_SERVER_ERROR: return "Internal Server Error";
        case Status::NOT_IMPLEMENTED: return "Not Implemented";
        case Status::BAD_GATEWAY: return "Bad Gateway";
        case Status::SERVICE_UNAVAILABLE: return "Service Unavailable";
        default: return "Unknown";
    }
}

} // namespace http

// WebSocket implementation
namespace websocket {

// WebSocketConnection implementation
bool WebSocketConnection::send_text(const std::string& text) {
    if (!conn_) return false;
    return mg_ws_send(conn_->get_mg_connection(), text.c_str(), text.size(), WEBSOCKET_OP_TEXT) > 0;
}

bool WebSocketConnection::send_binary(const void* data, size_t len) {
    if (!conn_) return false;
    return mg_ws_send(conn_->get_mg_connection(), data, len, WEBSOCKET_OP_BINARY) > 0;
}

bool WebSocketConnection::send_binary(const Buffer& buffer) {
    return send_binary(buffer.data(), buffer.size());
}

bool WebSocketConnection::send_ping(const std::string& data) {
    if (!conn_) return false;
    return mg_ws_send(conn_->get_mg_connection(), data.c_str(), data.size(), WEBSOCKET_OP_PING) > 0;
}

bool WebSocketConnection::send_pong(const std::string& data) {
    if (!conn_) return false;
    return mg_ws_send(conn_->get_mg_connection(), data.c_str(), data.size(), WEBSOCKET_OP_PONG) > 0;
}

bool WebSocketConnection::send_close(uint16_t code, const std::string& reason) {
    if (!conn_) return false;

    std::string close_data;
    close_data.resize(2 + reason.size());
    close_data[0] = (code >> 8) & 0xFF;
    close_data[1] = code & 0xFF;
    if (!reason.empty()) {
        std::copy(reason.begin(), reason.end(), close_data.begin() + 2);
    }

    return mg_ws_send(conn_->get_mg_connection(), close_data.c_str(), close_data.size(), WEBSOCKET_OP_CLOSE) > 0;
}

size_t WebSocketConnection::printf(const char* fmt, ...) {
    if (!conn_) return 0;
    va_list ap;
    va_start(ap, fmt);
    size_t result = mg_ws_vprintf(conn_->get_mg_connection(), WEBSOCKET_OP_TEXT, fmt, &ap);
    va_end(ap);
    return result;
}

size_t WebSocketConnection::vprintf(const char* fmt, va_list ap) {
    return conn_ ? mg_ws_vprintf(conn_->get_mg_connection(), WEBSOCKET_OP_TEXT, fmt, &ap) : 0;
}

// Server implementation
Server::Server(Manager& manager) : manager_(manager), upgrade_path_("/ws") {}

Server::~Server() {
    close_all_connections();
}

Server::Server(Server&& other) noexcept : manager_(other.manager_) {
    move_from(std::move(other));
}

Server& Server::operator=(Server&& other) noexcept {
    if (this != &other) {
        close_all_connections();
        move_from(std::move(other));
    }
    return *this;
}

void Server::listen(const std::string& address, const std::string& path) {
    upgrade_path_ = path;
    listener_ = manager_.listen(address,
        [this](Connection& conn, int event, void* event_data) {
            websocket_event_handler(conn, event, event_data);
        });

    if (listener_) {
        listener_->set_user_data(this);
    }
}

void Server::stop() {
    close_all_connections();
    if (listener_) {
        listener_->close();
        listener_.reset();
    }
}

bool Server::is_listening() const {
    return listener_ && listener_->is_valid() && listener_->is_listening();
}

std::vector<WebSocketConnection> Server::get_connections() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    std::vector<WebSocketConnection> result;

    for (unsigned long id : connections_) {
        if (auto conn = manager_.get_connection(id)) {
            result.emplace_back(conn);
        }
    }

    return result;
}

size_t Server::get_connection_count() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    return connections_.size();
}

void Server::broadcast_text(const std::string& text) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    for (unsigned long id : connections_) {
        if (auto conn = manager_.get_connection(id)) {
            WebSocketConnection ws_conn(conn);
            ws_conn.send_text(text);
        }
    }
}

void Server::broadcast_binary(const void* data, size_t len) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    for (unsigned long id : connections_) {
        if (auto conn = manager_.get_connection(id)) {
            WebSocketConnection ws_conn(conn);
            ws_conn.send_binary(data, len);
        }
    }
}

void Server::broadcast_binary(const Buffer& buffer) {
    broadcast_binary(buffer.data(), buffer.size());
}

void Server::close_all_connections() {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    for (unsigned long id : connections_) {
        if (auto conn = manager_.get_connection(id)) {
            conn->close();
        }
    }
    connections_.clear();
}

void Server::add_connection(unsigned long id) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    connections_.insert(id);
}

void Server::remove_connection(unsigned long id) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    connections_.erase(id);
}

void Server::move_from(Server&& other) {
    // manager_ is already initialized in constructor
    listener_ = std::move(other.listener_);
    connections_ = std::move(other.connections_);
    on_connect_ = std::move(other.on_connect_);
    on_message_ = std::move(other.on_message_);
    on_close_ = std::move(other.on_close_);
    on_error_ = std::move(other.on_error_);
    upgrade_path_ = std::move(other.upgrade_path_);
}

void Server::websocket_event_handler(Connection& conn, int event, void* event_data) {
    Server* server = static_cast<Server*>(conn.get_user_data());
    if (!server) return;

    switch (event) {
        case MG_EV_HTTP_MSG:
            server->handle_http_message(conn, static_cast<struct mg_http_message*>(event_data));
            break;
        case MG_EV_WS_OPEN:
            server->handle_websocket_open(conn, static_cast<struct mg_http_message*>(event_data));
            break;
        case MG_EV_WS_MSG:
            server->handle_websocket_message(conn, static_cast<struct mg_ws_message*>(event_data));
            break;
        case MG_EV_CLOSE:
            server->handle_websocket_close(conn);
            break;
    }
}

void Server::handle_http_message(Connection& conn, struct mg_http_message* msg) {
    std::string uri(msg->uri.buf, msg->uri.len);

    if (uri == upgrade_path_) {
        mg_ws_upgrade(conn.get_mg_connection(), msg, nullptr);
    } else {
        mg_http_reply(conn.get_mg_connection(), 404, "", "WebSocket endpoint not found");
    }
}

void Server::handle_websocket_open(Connection& conn, struct mg_http_message* msg) {
    add_connection(conn.get_id());

    if (on_connect_) {
        WebSocketConnection ws_conn(conn.shared_from_this());
        on_connect_(ws_conn);
    }
}

void Server::handle_websocket_message(Connection& conn, struct mg_ws_message* msg) {
    if (on_message_) {
        WebSocketConnection ws_conn(conn.shared_from_this());
        Message message(msg);
        on_message_(ws_conn, message);
    }
}

void Server::handle_websocket_close(Connection& conn) {
    remove_connection(conn.get_id());

    if (on_close_) {
        WebSocketConnection ws_conn(conn.shared_from_this());
        on_close_(ws_conn);
    }
}

// WebSocket utility functions
std::string message_type_to_string(MessageType type) {
    switch (type) {
        case MessageType::TEXT: return "text";
        case MessageType::BINARY: return "binary";
        case MessageType::CLOSE: return "close";
        case MessageType::PING: return "ping";
        case MessageType::PONG: return "pong";
        case MessageType::CONTINUE: return "continue";
        default: return "unknown";
    }
}

MessageType string_to_message_type(const std::string& type) {
    if (type == "text") return MessageType::TEXT;
    if (type == "binary") return MessageType::BINARY;
    if (type == "close") return MessageType::CLOSE;
    if (type == "ping") return MessageType::PING;
    if (type == "pong") return MessageType::PONG;
    if (type == "continue") return MessageType::CONTINUE;
    return MessageType::TEXT; // default
}

} // namespace websocket

// MQTT implementation
namespace mqtt {

struct mg_mqtt_opts Options::to_mg_opts() const {
    struct mg_mqtt_opts opts = {};
    opts.user.buf = const_cast<char*>(user.c_str());
    opts.user.len = user.size();
    opts.pass.buf = const_cast<char*>(password.c_str());
    opts.pass.len = password.size();
    opts.client_id.buf = const_cast<char*>(client_id.c_str());
    opts.client_id.len = client_id.size();
    opts.topic.buf = const_cast<char*>(topic.c_str());
    opts.topic.len = topic.size();
    opts.message.buf = const_cast<char*>(message.c_str());
    opts.message.len = message.size();
    opts.qos = static_cast<uint8_t>(qos);
    opts.version = version;
    opts.keepalive = keepalive;
    opts.retransmit_id = retransmit_id;
    opts.retain = retain;
    opts.clean = clean;
    return opts;
}

// Client implementation
Client::Client(Manager& manager) : manager_(manager) {}

void Client::connect(const std::string& url, const Options& options) {
    options_ = options;

    struct mg_mqtt_opts mg_opts = options.to_mg_opts();
    conn_ = manager_.connect(url, [this](Connection& conn, int event, void* event_data) {
        mqtt_event_handler(conn, event, event_data);
    });

    if (!conn_) {
        throw NetworkException("Failed to connect to MQTT broker: " + url);
    }

    // Set this client as user data for the connection
    conn_->set_user_data(this);

    // Send MQTT login
    mg_mqtt_login(conn_->get_mg_connection(), &mg_opts);
}

void Client::disconnect() {
    if (conn_) {
        conn_->close();
        conn_.reset();
    }
}

bool Client::is_connected() const {
    return conn_ && conn_->is_valid();
}

uint16_t Client::publish(const std::string& topic, const std::string& message, QoS qos, bool retain) {
    if (!conn_) return 0;

    Options pub_opts = create_publish_options(topic, message, qos, retain);
    struct mg_mqtt_opts mg_opts = pub_opts.to_mg_opts();

    return mg_mqtt_pub(conn_->get_mg_connection(), &mg_opts);
}

uint16_t Client::publish(const Options& options) {
    if (!conn_) return 0;

    struct mg_mqtt_opts mg_opts = options.to_mg_opts();
    return mg_mqtt_pub(conn_->get_mg_connection(), &mg_opts);
}

void Client::subscribe(const std::string& topic, QoS qos) {
    if (!conn_) return;

    Options sub_opts = create_subscribe_options(topic, qos);
    struct mg_mqtt_opts mg_opts = sub_opts.to_mg_opts();

    mg_mqtt_sub(conn_->get_mg_connection(), &mg_opts);
}

void Client::unsubscribe(const std::string& topic) {
    if (!conn_) return;

    Options unsub_opts;
    unsub_opts.topic = topic;
    struct mg_mqtt_opts mg_opts = unsub_opts.to_mg_opts();

    // Note: mongoose doesn't have mg_mqtt_unsub, we'd need to implement it
    // For now, this is a placeholder
}

void Client::ping() {
    if (conn_) {
        mg_mqtt_ping(conn_->get_mg_connection());
    }
}

void Client::pong() {
    if (conn_) {
        mg_mqtt_pong(conn_->get_mg_connection());
    }
}

unsigned long Client::get_connection_id() const {
    return conn_ ? conn_->get_id() : 0;
}

Address Client::get_remote_address() const {
    return conn_ ? conn_->get_remote_address() : Address();
}

void Client::set_user_data(void* data) {
    if (conn_) conn_->set_user_data(data);
}

void* Client::get_user_data() const {
    return conn_ ? conn_->get_user_data() : nullptr;
}

void Client::mqtt_event_handler(Connection& conn, int event, void* event_data) {
    Client* client = static_cast<Client*>(conn.get_user_data());
    if (!client) return;

    switch (event) {
        case MG_EV_MQTT_OPEN:
            client->handle_mqtt_open(conn, static_cast<int*>(event_data));
            break;
        case MG_EV_MQTT_MSG:
            client->handle_mqtt_message(conn, static_cast<struct mg_mqtt_message*>(event_data));
            break;
        case MG_EV_CLOSE:
            client->handle_mqtt_close(conn);
            break;
    }
}

void Client::handle_mqtt_open(Connection& conn, int* connack_status) {
    (void)conn; (void)connack_status; // Suppress unused parameter warnings
    if (on_connect_) {
        on_connect_();
    }
}

void Client::handle_mqtt_message(Connection& conn, struct mg_mqtt_message* msg) {
    (void)conn; // Suppress unused parameter warning
    if (on_message_) {
        Message message(msg);
        on_message_(message);
    }
}

void Client::handle_mqtt_close(Connection& conn) {
    (void)conn; // Suppress unused parameter warning
    if (on_disconnect_) {
        on_disconnect_();
    }
}

Options Client::create_publish_options(const std::string& topic, const std::string& message, QoS qos, bool retain) {
    Options opts = options_;
    opts.topic = topic;
    opts.message = message;
    opts.qos = qos;
    opts.retain = retain;
    return opts;
}

Options Client::create_subscribe_options(const std::string& topic, QoS qos) {
    Options opts = options_;
    opts.topic = topic;
    opts.qos = qos;
    return opts;
}

std::string qos_to_string(QoS qos) {
    switch (qos) {
        case QoS::AT_MOST_ONCE: return "0";
        case QoS::AT_LEAST_ONCE: return "1";
        case QoS::EXACTLY_ONCE: return "2";
        default: return "unknown";
    }
}

QoS string_to_qos(const std::string& qos) {
    if (qos == "0") return QoS::AT_MOST_ONCE;
    if (qos == "1") return QoS::AT_LEAST_ONCE;
    if (qos == "2") return QoS::EXACTLY_ONCE;
    return QoS::AT_MOST_ONCE;
}

} // namespace mqtt

// File serving implementation
namespace file {

struct mg_http_serve_opts ServeOptions::to_mg_opts() const {
    struct mg_http_serve_opts opts = {};
    opts.root_dir = root_dir.c_str();
    opts.ssi_pattern = ssi_pattern.empty() ? nullptr : ssi_pattern.c_str();
    opts.extra_headers = extra_headers.empty() ? nullptr : extra_headers.c_str();
    opts.mime_types = mime_types.empty() ? nullptr : mime_types.c_str();
    opts.page404 = page404.empty() ? nullptr : page404.c_str();
    opts.fs = nullptr; // Use default filesystem
    return opts;
}

FileServer::FileServer(Manager& manager) : manager_(manager) {}

void FileServer::listen(const std::string& address, const ServeOptions& options) {
    options_ = options;

    listener_ = manager_.listen(address,
        [this](Connection& conn, int event, void* event_data) {
            file_event_handler(conn, event, event_data);
        });

    if (listener_) {
        listener_->set_user_data(this);
    }
}

void FileServer::stop() {
    if (listener_) {
        listener_->close();
        listener_.reset();
    }
}

bool FileServer::is_listening() const {
    return listener_ && listener_->is_valid() && listener_->is_listening();
}

void FileServer::file_event_handler(Connection& conn, int event, void* event_data) {
    if (event == MG_EV_HTTP_MSG) {
        FileServer* server = static_cast<FileServer*>(conn.get_user_data());
        if (server) {
            server->handle_http_message(conn, static_cast<struct mg_http_message*>(event_data));
        }
    }
}

void FileServer::handle_http_message(Connection& conn, struct mg_http_message* msg) {
    struct mg_http_serve_opts mg_opts = options_.to_mg_opts();
    mg_http_serve_dir(conn.get_mg_connection(), msg, &mg_opts);
}

} // namespace file

// Utility functions implementation
std::string url_encode(const std::string& str) {
    std::ostringstream encoded;
    for (char c : str) {
        if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            encoded << c;
        } else {
            encoded << '%' << std::hex << std::uppercase << (unsigned char)c;
        }
    }
    return encoded.str();
}

std::string url_decode(const std::string& str) {
    std::string decoded;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%' && i + 2 < str.length()) {
            int value;
            std::istringstream iss(str.substr(i + 1, 2));
            if (iss >> std::hex >> value) {
                decoded += static_cast<char>(value);
                i += 2;
            } else {
                decoded += str[i];
            }
        } else if (str[i] == '+') {
            decoded += ' ';
        } else {
            decoded += str[i];
        }
    }
    return decoded;
}

std::string base64_encode(const std::string& str) {
    // Simple base64 encoding implementation
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    std::string encoded;
    int val = 0, valb = -6;

    for (unsigned char c : str) {
        val = (val << 8) + c;
        valb += 8;
        while (valb >= 0) {
            encoded.push_back(chars[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }

    if (valb > -6) {
        encoded.push_back(chars[((val << 8) >> (valb + 8)) & 0x3F]);
    }

    while (encoded.size() % 4) {
        encoded.push_back('=');
    }

    return encoded;
}

std::string base64_decode(const std::string& str) {
    // Simple base64 decoding implementation
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    std::string decoded;
    int val = 0, valb = -8;

    for (char c : str) {
        if (c == '=') break;
        size_t pos = chars.find(c);
        if (pos == std::string::npos) continue;

        val = (val << 6) + pos;
        valb += 6;
        if (valb >= 0) {
            decoded.push_back(char((val >> valb) & 0xFF));
            valb -= 8;
        }
    }

    return decoded;
}

} // namespace mongoose_cpp
