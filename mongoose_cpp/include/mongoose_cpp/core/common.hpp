#pragma once

#include <memory>
#include <string>
#include <functional>
#include <stdexcept>
#include <vector>
#include <map>
#include <chrono>
#include <future>
#include <type_traits>

extern "C" {
#include "mongoose.h"
}

namespace mongoose_cpp {

// Forward declarations
class Manager;
class Connection;
class HttpServer;
class WebSocketServer;
class MqttClient;

// Common types and aliases
using EventHandler = std::function<void(Connection&, int event, void* event_data)>;
using HttpHandler = std::function<void(Connection&, struct mg_http_message*)>;
using WebSocketHandler = std::function<void(Connection&, struct mg_ws_message*)>;
using MqttHandler = std::function<void(Connection&, struct mg_mqtt_message*)>;
using TimerCallback = std::function<void()>;

// Time utilities
using TimePoint = std::chrono::steady_clock::time_point;
using Duration = std::chrono::milliseconds;

// Exception classes
class MongooseException : public std::runtime_error {
public:
    explicit MongooseException(const std::string& message) 
        : std::runtime_error("Mongoose Error: " + message) {}
};

class NetworkException : public MongooseException {
public:
    explicit NetworkException(const std::string& message) 
        : MongooseException("Network Error: " + message) {}
};

class HttpException : public MongooseException {
public:
    explicit HttpException(const std::string& message) 
        : MongooseException("HTTP Error: " + message) {}
};

class WebSocketException : public MongooseException {
public:
    explicit WebSocketException(const std::string& message) 
        : MongooseException("WebSocket Error: " + message) {}
};

class MqttException : public MongooseException {
public:
    explicit MqttException(const std::string& message) 
        : MongooseException("MQTT Error: " + message) {}
};

// String utilities
class StringView {
private:
    const char* data_;
    size_t size_;

public:
    StringView() : data_(nullptr), size_(0) {}
    StringView(const char* str) : data_(str), size_(str ? strlen(str) : 0) {}
    StringView(const char* str, size_t len) : data_(str), size_(len) {}
    StringView(const std::string& str) : data_(str.c_str()), size_(str.size()) {}
    StringView(const struct mg_str& mg_str) : data_(mg_str.buf), size_(mg_str.len) {}

    const char* data() const { return data_; }
    size_t size() const { return size_; }
    bool empty() const { return size_ == 0; }

    std::string to_string() const {
        return data_ ? std::string(data_, size_) : std::string();
    }

    operator std::string() const { return to_string(); }

    struct mg_str to_mg_str() const {
        return {data_, size_};
    }

    bool operator==(const StringView& other) const {
        return size_ == other.size_ && 
               (size_ == 0 || memcmp(data_, other.data_, size_) == 0);
    }

    bool operator!=(const StringView& other) const {
        return !(*this == other);
    }
};

// Buffer management
class Buffer {
private:
    std::vector<char> data_;

public:
    Buffer() = default;
    explicit Buffer(size_t size) : data_(size) {}
    Buffer(const void* data, size_t size) : data_(static_cast<const char*>(data), 
                                                  static_cast<const char*>(data) + size) {}

    char* data() { return data_.data(); }
    const char* data() const { return data_.data(); }
    size_t size() const { return data_.size(); }
    bool empty() const { return data_.empty(); }

    void resize(size_t size) { data_.resize(size); }
    void reserve(size_t size) { data_.reserve(size); }
    void clear() { data_.clear(); }

    void append(const void* data, size_t size) {
        const char* char_data = static_cast<const char*>(data);
        data_.insert(data_.end(), char_data, char_data + size);
    }

    void append(const std::string& str) {
        append(str.data(), str.size());
    }

    std::string to_string() const {
        return std::string(data_.begin(), data_.end());
    }
};

// RAII wrapper for mg_str
class ManagedString {
private:
    std::unique_ptr<char[]> data_;
    size_t size_;

public:
    ManagedString() : data_(nullptr), size_(0) {}
    
    explicit ManagedString(const std::string& str) 
        : data_(std::make_unique<char[]>(str.size() + 1)), size_(str.size()) {
        std::copy(str.begin(), str.end(), data_.get());
        data_[size_] = '\0';
    }

    explicit ManagedString(const char* str) 
        : ManagedString(std::string(str ? str : "")) {}

    ManagedString(const ManagedString&) = delete;
    ManagedString& operator=(const ManagedString&) = delete;

    ManagedString(ManagedString&& other) noexcept 
        : data_(std::move(other.data_)), size_(other.size_) {
        other.size_ = 0;
    }

    ManagedString& operator=(ManagedString&& other) noexcept {
        if (this != &other) {
            data_ = std::move(other.data_);
            size_ = other.size_;
            other.size_ = 0;
        }
        return *this;
    }

    const char* c_str() const { return data_ ? data_.get() : ""; }
    size_t size() const { return size_; }
    bool empty() const { return size_ == 0; }

    struct mg_str to_mg_str() const {
        return {data_.get(), size_};
    }

    StringView to_string_view() const {
        return StringView(data_.get(), size_);
    }
};

// Utility functions
template<typename T>
typename std::enable_if<std::is_integral<T>::value, T>::type
safe_cast(const char* str, T default_value = T{}) {
    if (!str) return default_value;
    try {
        if constexpr (std::is_same_v<T, int>) {
            return std::stoi(str);
        } else if constexpr (std::is_same_v<T, long>) {
            return std::stol(str);
        } else if constexpr (std::is_same_v<T, long long>) {
            return std::stoll(str);
        } else if constexpr (std::is_same_v<T, unsigned long>) {
            return std::stoul(str);
        } else if constexpr (std::is_same_v<T, unsigned long long>) {
            return std::stoull(str);
        }
    } catch (...) {
        return default_value;
    }
    return default_value;
}

template<typename T>
typename std::enable_if<std::is_floating_point<T>::value, T>::type
safe_cast(const char* str, T default_value = T{}) {
    if (!str) return default_value;
    try {
        if constexpr (std::is_same_v<T, float>) {
            return std::stof(str);
        } else if constexpr (std::is_same_v<T, double>) {
            return std::stod(str);
        } else if constexpr (std::is_same_v<T, long double>) {
            return std::stold(str);
        }
    } catch (...) {
        return default_value;
    }
    return default_value;
}

// URL encoding/decoding utilities
std::string url_encode(const std::string& str);
std::string url_decode(const std::string& str);

// Base64 encoding/decoding utilities  
std::string base64_encode(const std::string& str);
std::string base64_decode(const std::string& str);

} // namespace mongoose_cpp
