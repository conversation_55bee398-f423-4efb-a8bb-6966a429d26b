#pragma once

#include "common.hpp"
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <unordered_map>

namespace mongoose_cpp {

class Timer;
class Connection;

// RAII wrapper for mg_mgr
class Manager {
private:
    struct mg_mgr mgr_;
    std::atomic<bool> running_;
    std::unique_ptr<std::thread> poll_thread_;
    mutable std::mutex connections_mutex_;
    std::unordered_map<unsigned long, std::shared_ptr<Connection>> connections_;
    std::unordered_map<unsigned long, std::shared_ptr<Timer>> timers_;
    mutable std::mutex timers_mutex_;
    std::condition_variable stop_cv_;
    std::mutex stop_mutex_;

    // Internal event handler that dispatches to C++ handlers
    static void internal_event_handler(struct mg_connection* c, int ev, void* ev_data);

public:
    Manager();
    ~Manager();

    // Non-copyable, movable
    Manager(const Manager&) = delete;
    Manager& operator=(const Manager&) = delete;
    Manager(Manager&& other) noexcept;
    Manager& operator=(Manager&& other) noexcept;

    // Core functionality
    void poll(int timeout_ms = 1000);
    void start_polling_thread();
    void stop_polling_thread();
    bool is_running() const { return running_.load(); }

    // Connection management
    std::shared_ptr<Connection> listen(const std::string& url, EventHandler handler);
    std::shared_ptr<Connection> connect(const std::string& url, EventHandler handler);
    std::shared_ptr<Connection> wrap_fd(int fd, EventHandler handler);

    // Timer management
    std::shared_ptr<Timer> add_timer(Duration interval, TimerCallback callback, bool repeat = false);
    void remove_timer(unsigned long timer_id);

    // Configuration
    void set_dns_timeout(int timeout_ms);
    void set_user_data(void* data);
    void* get_user_data() const;

    // TLS configuration
    void set_tls_context(void* tls_ctx);
    void* get_tls_context() const;

    // Wakeup functionality
    bool wakeup(unsigned long connection_id, const void* data, size_t len);
    bool init_wakeup();

    // Internal access (for Connection and other classes)
    struct mg_mgr* get_mgr() { return &mgr_; }
    const struct mg_mgr* get_mgr() const { return &mgr_; }

    // Connection registration/unregistration (called by Connection)
    void register_connection(unsigned long id, std::shared_ptr<Connection> conn);
    void unregister_connection(unsigned long id);
    std::shared_ptr<Connection> get_connection(unsigned long id) const;

    // Timer registration/unregistration (called by Timer)
    void register_timer(unsigned long id, std::shared_ptr<Timer> timer);
    void unregister_timer_internal(unsigned long id);

private:
    void cleanup();
    void move_from(Manager&& other);
};

// Timer class
class Timer {
private:
    struct mg_timer* timer_;
    unsigned long id_;
    TimerCallback callback_;
    Manager* manager_;
    bool repeat_;

    static void timer_callback(void* arg);

public:
    Timer(Manager* mgr, Duration interval, TimerCallback callback, bool repeat);
    ~Timer();

    // Non-copyable, movable
    Timer(const Timer&) = delete;
    Timer& operator=(const Timer&) = delete;
    Timer(Timer&& other) noexcept;
    Timer& operator=(Timer&& other) noexcept;

    unsigned long get_id() const { return id_; }
    bool is_repeating() const { return repeat_; }
    void cancel();

private:
    void cleanup();
    void move_from(Timer&& other);
};

// Inline implementations for simple methods
inline void Manager::set_dns_timeout(int timeout_ms) {
    mgr_.dnstimeout = timeout_ms;
}

inline void Manager::set_user_data(void* data) {
    mgr_.userdata = data;
}

inline void* Manager::get_user_data() const {
    return mgr_.userdata;
}

inline void Manager::set_tls_context(void* tls_ctx) {
    mgr_.tls_ctx = tls_ctx;
}

inline void* Manager::get_tls_context() const {
    return mgr_.tls_ctx;
}

inline bool Manager::wakeup(unsigned long connection_id, const void* data, size_t len) {
    return mg_wakeup(&mgr_, connection_id, data, len);
}

inline bool Manager::init_wakeup() {
    return mg_wakeup_init(&mgr_);
}

} // namespace mongoose_cpp
