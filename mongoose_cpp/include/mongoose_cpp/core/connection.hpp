#pragma once

#include "common.hpp"
#include <memory>
#include <atomic>

namespace mongoose_cpp {

class Manager;

// Address wrapper
class Address {
private:
    struct mg_addr addr_;

public:
    Address() { memset(&addr_, 0, sizeof(addr_)); }
    explicit Address(const struct mg_addr& addr) : addr_(addr) {}

    std::string to_string() const;
    uint16_t port() const;
    bool is_ip4() const { return addr_.is_ip6 == 0; }
    bool is_ip6() const { return addr_.is_ip6 != 0; }

    const struct mg_addr& get_mg_addr() const { return addr_; }
};

// RAII wrapper for mg_connection
class Connection : public std::enable_shared_from_this<Connection> {
private:
    struct mg_connection* conn_;
    Manager* manager_;
    EventHandler event_handler_;
    std::atomic<bool> valid_;

    // Private constructor - only Manager can create connections
    Connection(struct mg_connection* conn, Manager* mgr, EventHandler handler);

public:
    ~Connection();

    // Non-copyable, non-movable (managed by shared_ptr)
    Connection(const Connection&) = delete;
    Connection& operator=(const Connection&) = delete;
    Connection(Connection&&) = delete;
    Connection& operator=(Connection&&) = delete;

    // Factory method (called by Manager)
    static std::shared_ptr<Connection> create(struct mg_connection* conn, Manager* mgr, EventHandler handler);

    // Connection properties
    bool is_valid() const { return valid_.load() && conn_ != nullptr; }
    unsigned long get_id() const;
    Address get_local_address() const;
    Address get_remote_address() const;
    
    // Connection state
    bool is_listening() const;
    bool is_client() const;
    bool is_accepted() const;
    bool is_resolving() const;
    bool is_connecting() const;
    bool is_tls() const;
    bool is_tls_handshake() const;

    // Data transmission
    bool send(const void* data, size_t len);
    bool send(const std::string& data);
    bool send(const Buffer& buffer);
    size_t printf(const char* fmt, ...);
    size_t vprintf(const char* fmt, va_list ap);

    // Buffer access
    Buffer get_recv_buffer() const;
    Buffer get_send_buffer() const;
    void clear_recv_buffer();
    void clear_send_buffer();

    // Connection management
    void close();
    void mark_for_close() { close(); }

    // User data
    void set_user_data(void* data);
    void* get_user_data() const;

    // Protocol-specific data
    void set_protocol_data(void* data);
    void* get_protocol_data() const;

    // TLS
    void* get_tls() const;

    // Event handling
    void set_event_handler(EventHandler handler);
    EventHandler get_event_handler() const { return event_handler_; }

    // Internal access
    struct mg_connection* get_mg_connection() { return conn_; }
    const struct mg_connection* get_mg_connection() const { return conn_; }

    // Internal event dispatch (called by Manager)
    void dispatch_event(int event, void* event_data);

private:
    void invalidate();
    friend class Manager;
};

// Inline implementations
inline unsigned long Connection::get_id() const {
    return conn_ ? conn_->id : 0;
}

inline Address Connection::get_local_address() const {
    return conn_ ? Address(conn_->loc) : Address();
}

inline Address Connection::get_remote_address() const {
    return conn_ ? Address(conn_->rem) : Address();
}

inline bool Connection::is_listening() const {
    return conn_ && conn_->is_listening;
}

inline bool Connection::is_client() const {
    return conn_ && conn_->is_client;
}

inline bool Connection::is_accepted() const {
    return conn_ && conn_->is_accepted;
}

inline bool Connection::is_resolving() const {
    return conn_ && conn_->is_resolving;
}

inline bool Connection::is_connecting() const {
    return conn_ && conn_->is_connecting;
}

inline bool Connection::is_tls() const {
    return conn_ && conn_->is_tls;
}

inline bool Connection::is_tls_handshake() const {
    return conn_ && conn_->is_tls_hs;
}

inline bool Connection::send(const void* data, size_t len) {
    return conn_ && mg_send(conn_, data, len);
}

inline bool Connection::send(const std::string& data) {
    return send(data.data(), data.size());
}

inline bool Connection::send(const Buffer& buffer) {
    return send(buffer.data(), buffer.size());
}

inline size_t Connection::printf(const char* fmt, ...) {
    if (!conn_) return 0;
    va_list ap;
    va_start(ap, fmt);
    size_t result = mg_vprintf(conn_, fmt, &ap);
    va_end(ap);
    return result;
}

inline size_t Connection::vprintf(const char* fmt, va_list ap) {
    return conn_ ? mg_vprintf(conn_, fmt, &ap) : 0;
}

inline void Connection::set_user_data(void* data) {
    if (conn_) conn_->fn_data = data;
}

inline void* Connection::get_user_data() const {
    return conn_ ? conn_->fn_data : nullptr;
}

inline void Connection::set_protocol_data(void* data) {
    if (conn_) conn_->pfn_data = data;
}

inline void* Connection::get_protocol_data() const {
    return conn_ ? conn_->pfn_data : nullptr;
}

inline void* Connection::get_tls() const {
    return conn_ ? conn_->tls : nullptr;
}

} // namespace mongoose_cpp
