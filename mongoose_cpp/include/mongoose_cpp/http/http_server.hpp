#pragma once

#include "../core/common.hpp"
#include "../core/manager.hpp"
#include "../core/connection.hpp"
#include <unordered_map>
#include <regex>

namespace mongoose_cpp {
namespace http {

// HTTP method enumeration
enum class Method {
    GET, POST, PUT, DELETE, HEAD, OPTIONS, PATCH, CONNECT, TRACE, UNKNOWN
};

// HTTP status codes
enum class Status {
    OK = 200,
    CREATED = 201,
    ACCEPTED = 202,
    NO_CONTENT = 204,
    MOVED_PERMANENTLY = 301,
    FOUND = 302,
    NOT_MODIFIED = 304,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    METHOD_NOT_ALLOWED = 405,
    CONFLICT = 409,
    INTERNAL_SERVER_ERROR = 500,
    NOT_IMPLEMENTED = 501,
    BAD_GATEWAY = 502,
    SERVICE_UNAVAILABLE = 503
};

// HTTP header
class Header {
private:
    std::string name_;
    std::string value_;

public:
    Header() = default;
    Header(const std::string& name, const std::string& value) : name_(name), value_(value) {}
    Header(const struct mg_http_header& header) 
        : name_(header.name.buf, header.name.len), value_(header.value.buf, header.value.len) {}

    const std::string& name() const { return name_; }
    const std::string& value() const { return value_; }
    
    void set_name(const std::string& name) { name_ = name; }
    void set_value(const std::string& value) { value_ = value; }
};

// HTTP request/response message
class Message {
private:
    struct mg_http_message* msg_;
    std::vector<Header> headers_;
    bool headers_parsed_;

    void parse_headers();

public:
    explicit Message(struct mg_http_message* msg);

    // Request line
    StringView method() const;
    StringView uri() const;
    StringView query() const;
    StringView proto() const;

    // Body
    StringView body() const;
    StringView head() const;
    StringView message() const;

    // Headers
    const std::vector<Header>& headers();
    std::string get_header(const std::string& name) const;
    bool has_header(const std::string& name) const;

    // Query parameters
    std::string get_query_param(const std::string& name) const;
    std::map<std::string, std::string> get_query_params() const;

    // Form data
    std::string get_form_param(const std::string& name) const;
    std::map<std::string, std::string> get_form_params() const;

    // Utility methods
    Method get_method() const;
    std::string get_path() const;
    bool is_websocket_upgrade() const;

    // Authentication
    std::pair<std::string, std::string> get_basic_auth() const;

    // Content type
    std::string get_content_type() const;
    size_t get_content_length() const;

    // Internal access
    struct mg_http_message* get_mg_message() { return msg_; }
    const struct mg_http_message* get_mg_message() const { return msg_; }
};

// HTTP response builder
class Response {
private:
    Connection& conn_;
    Status status_;
    std::map<std::string, std::string> headers_;
    std::string body_;
    bool sent_;

public:
    explicit Response(Connection& conn) : conn_(conn), status_(Status::OK), sent_(false) {}
    ~Response();

    // Non-copyable, movable
    Response(const Response&) = delete;
    Response& operator=(const Response&) = delete;
    Response(Response&& other) noexcept;
    Response& operator=(Response&& other) noexcept;

    // Status
    Response& status(Status status) { status_ = status; return *this; }
    Response& status(int status) { status_ = static_cast<Status>(status); return *this; }

    // Headers
    Response& header(const std::string& name, const std::string& value);
    Response& content_type(const std::string& type);
    Response& content_length(size_t length);

    // Body
    Response& body(const std::string& body) { body_ = body; return *this; }
    Response& body(const char* body) { body_ = body ? body : ""; return *this; }
    Response& json(const std::string& json);
    Response& html(const std::string& html);
    Response& text(const std::string& text);

    // Send response
    void send();
    void send(const std::string& body);
    void send(Status status, const std::string& body);

    // Chunked response
    void send_chunk(const std::string& chunk);
    void end_chunks();

    // File response
    void send_file(const std::string& path);
    void send_file(const std::string& path, const std::string& content_type);

    // Redirect
    void redirect(const std::string& url, Status status = Status::FOUND);

    // Error responses
    void error(Status status, const std::string& message = "");

private:
    void ensure_not_sent();
    std::string build_headers();
};

// Route handler
using RouteHandler = std::function<void(const Message&, Response&)>;

// Route pattern
class Route {
private:
    std::string pattern_;
    std::regex regex_;
    RouteHandler handler_;
    Method method_;

public:
    Route(Method method, const std::string& pattern, RouteHandler handler);

    bool matches(Method method, const std::string& path) const;
    void handle(const Message& request, Response& response) const;
    
    Method get_method() const { return method_; }
    const std::string& get_pattern() const { return pattern_; }

private:
    std::regex compile_pattern(const std::string& pattern);
};

// HTTP server
class Server {
private:
    Manager& manager_;
    std::shared_ptr<Connection> listener_;
    std::vector<Route> routes_;
    std::string document_root_;
    bool serve_static_;

    static void http_event_handler(Connection& conn, int event, void* event_data);
    void handle_http_message(Connection& conn, struct mg_http_message* msg);
    bool handle_route(const Message& request, Response& response);
    void handle_static_file(const Message& request, Response& response);

public:
    explicit Server(Manager& manager);
    ~Server() = default;

    // Non-copyable, movable
    Server(const Server&) = delete;
    Server& operator=(const Server&) = delete;
    Server(Server&& other) noexcept = default;
    Server& operator=(Server&& other) noexcept = default;

    // Server control
    void listen(const std::string& address);
    void stop();
    bool is_listening() const;

    // Route registration
    Server& get(const std::string& pattern, RouteHandler handler);
    Server& post(const std::string& pattern, RouteHandler handler);
    Server& put(const std::string& pattern, RouteHandler handler);
    Server& delete_(const std::string& pattern, RouteHandler handler);
    Server& head(const std::string& pattern, RouteHandler handler);
    Server& options(const std::string& pattern, RouteHandler handler);
    Server& patch(const std::string& pattern, RouteHandler handler);
    Server& route(Method method, const std::string& pattern, RouteHandler handler);

    // Static file serving
    Server& static_files(const std::string& document_root);
    Server& disable_static_files();

    // Middleware (future extension point)
    // Server& use(MiddlewareHandler handler);
};

// Utility functions
std::string method_to_string(Method method);
Method string_to_method(const std::string& method);
std::string status_to_string(Status status);
const char* status_to_reason_phrase(Status status);

} // namespace http
} // namespace mongoose_cpp
