#include "mongoose_cpp.hpp"
#include <iostream>

using namespace mongoose_cpp;

int main() {
    std::cout << "Testing basic functionality..." << std::endl;
    
    try {
        // Test 1: Create Manager
        std::cout << "Creating Manager..." << std::endl;
        Manager manager;
        std::cout << "Manager created successfully" << std::endl;
        
        // Test 2: Create HTTP Server
        std::cout << "Creating HTTP Server..." << std::endl;
        http::Server server(manager);
        std::cout << "HTTP Server created successfully" << std::endl;
        
        // Test 3: Add a simple route
        std::cout << "Adding route..." << std::endl;
        server.get("/test", [](const http::Message& req, http::Response& res) {
            (void)req; // Suppress unused parameter warning
            res.text("Hello World");
        });
        std::cout << "Route added successfully" << std::endl;
        
        // Test 4: Try to listen (this might be where it crashes)
        std::cout << "Attempting to listen..." << std::endl;
        server.listen("http://0.0.0.0:8080");
        std::cout << "Listen successful" << std::endl;
        
        // Test 5: Poll once
        std::cout << "Polling once..." << std::endl;
        manager.poll(100);
        std::cout << "Poll successful" << std::endl;
        
        std::cout << "All tests passed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
