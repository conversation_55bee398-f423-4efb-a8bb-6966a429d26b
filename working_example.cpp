#include "mongoose_cpp.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace mongoose_cpp;

int main() {
    std::cout << "Mongoose C++ Working Example" << std::endl;
    std::cout << "============================" << std::endl;
    
    try {
        Manager manager;
        http::Server server(manager);
        
        // Setup routes
        server.get("/", [](const http::Message& req, http::Response& res) {
            (void)req; // Suppress unused parameter warning
            res.html("<h1>Welcome to Mongoose C++!</h1>"
                    "<p><a href='/api/hello'>API Example</a></p>"
                    "<p><a href='/json'>JSON Example</a></p>");
        });
        
        server.get("/api/hello", [](const http::Message& req, http::Response& res) {
            std::string name = req.get_query_param("name");
            if (name.empty()) name = "World";
            res.json("{\"message\": \"Hello, " + name + "!\"}");
        });
        
        server.get("/json", [](const http::Message& req, http::Response& res) {
            (void)req; // Suppress unused parameter warning
            res.json("{\"status\": \"ok\", \"timestamp\": " + 
                    std::to_string(std::time(nullptr)) + "}");
        });
        
        // Start server
        server.listen("http://0.0.0.0:8888");
        std::cout << "HTTP Server listening on http://localhost:8888" << std::endl;
        std::cout << "Press Ctrl+C to stop the server" << std::endl;
        
        // Simple polling loop instead of background thread
        for (int i = 0; i < 300; ++i) { // Run for 5 minutes (300 * 1 second)
            manager.poll(1000);
        }
        
        std::cout << "Server stopped" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
