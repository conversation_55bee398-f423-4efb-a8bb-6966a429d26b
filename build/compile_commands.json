[{"directory": "/home/<USER>/onvif/build", "command": "/usr/bin/gcc -DMG_ENABLE_IPV6=1 -DMG_ENABLE_LINES=1 -DMG_ENABLE_LOG=1   -Wall -Wextra -O3 -DNDEBUG -std=gnu99 -o CMakeFiles/mongoose.dir/mongoose.c.o -c /home/<USER>/onvif/mongoose.c", "file": "/home/<USER>/onvif/mongoose.c"}, {"directory": "/home/<USER>/onvif/build", "command": "/usr/bin/g++ -DMG_ENABLE_IPV6=1 -DMG_ENABLE_LINES=1 -DMG_ENABLE_LOG=1 -I/home/<USER>/onvif  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -std=c++11 -o CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o -c /home/<USER>/onvif/mongoose_cpp.cpp", "file": "/home/<USER>/onvif/mongoose_cpp.cpp"}, {"directory": "/home/<USER>/onvif/build", "command": "/usr/bin/g++ -D<PERSON>_ENABLE_IPV6=1 -DMG_ENABLE_LINES=1 -DMG_ENABLE_LOG=1 -I/home/<USER>/onvif  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -std=c++11 -o CMakeFiles/example.dir/example.cpp.o -c /home/<USER>/onvif/example.cpp", "file": "/home/<USER>/onvif/example.cpp"}]