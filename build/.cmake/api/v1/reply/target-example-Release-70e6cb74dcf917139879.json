{"artifacts": [{"path": "example"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "add_definitions"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 67, "parent": 0}, {"command": 1, "file": 0, "line": 71, "parent": 0}, {"command": 2, "file": 0, "line": 32, "parent": 0}, {"command": 2, "file": 0, "line": 31, "parent": 0}, {"command": 2, "file": 0, "line": 33, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -O3 -DNDEBUG"}, {"fragment": "-std=c++11"}], "defines": [{"backtrace": 3, "define": "MG_ENABLE_IPV6=1"}, {"backtrace": 4, "define": "MG_ENABLE_LINES=1"}, {"backtrace": 5, "define": "MG_ENABLE_LOG=1"}], "includes": [{"backtrace": 2, "path": "/home/<USER>/onvif"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "mongoose::@6890427a1f51a3e7e1df"}, {"backtrace": 2, "id": "mongoose_cpp::@6890427a1f51a3e7e1df"}], "id": "example::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wall -Wextra -Wpedantic -O3 -DNDEBUG", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "libmongoose_cpp.a", "role": "libraries"}, {"backtrace": 2, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-lrt", "role": "libraries"}, {"fragment": "libmongoose.a", "role": "libraries"}], "language": "CXX"}, "name": "example", "nameOnDisk": "example", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "example.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}