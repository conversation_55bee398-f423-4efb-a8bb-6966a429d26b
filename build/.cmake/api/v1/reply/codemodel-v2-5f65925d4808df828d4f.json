{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-2637f8105d1701a68baa.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "mongoose_cpp", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "example::@6890427a1f51a3e7e1df", "jsonFile": "target-example-Release-70e6cb74dcf917139879.json", "name": "example", "projectIndex": 0}, {"directoryIndex": 0, "id": "mongoose::@6890427a1f51a3e7e1df", "jsonFile": "target-mongoose-Release-539fa2cf80228efdc119.json", "name": "mongoose", "projectIndex": 0}, {"directoryIndex": 0, "id": "mongoose_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-mongoose_cpp-Release-b79c099fce99685b8085.json", "name": "mongoose_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_mongoose_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-test_mongoose_cpp-Release-cd15f248113bb4bc0830.json", "name": "test_mongoose_cpp", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/onvif/build", "source": "/home/<USER>/onvif"}, "version": {"major": 2, "minor": 3}}