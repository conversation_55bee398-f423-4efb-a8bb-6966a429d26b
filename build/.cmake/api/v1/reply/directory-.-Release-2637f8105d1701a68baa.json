{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 87, "parent": 0}, {"command": 0, "file": 0, "line": 93, "parent": 0}, {"command": 0, "file": 0, "line": 116, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["libmongoose_cpp.a"], "targetId": "mongoose_cpp::@6890427a1f51a3e7e1df", "targetIndex": 2, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["libmongoose.a"], "targetId": "mongoose::@6890427a1f51a3e7e1df", "targetIndex": 1, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "include", "paths": ["mongoose_cpp.hpp", "mongoose.h"], "type": "file"}, {"backtrace": 3, "component": "Unspecified", "destination": "lib/cmake/mongoose_cpp", "paths": ["build/mongoose_cpp-config.cmake", "build/mongoose_cpp-config-version.cmake"], "type": "file"}], "paths": {"build": ".", "source": "."}}