# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/onvif

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/onvif/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles /home/<USER>/onvif/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named mongoose

# Build rule for target.
mongoose: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mongoose
.PHONY : mongoose

# fast build rule for target.
mongoose/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose.dir/build.make CMakeFiles/mongoose.dir/build
.PHONY : mongoose/fast

#=============================================================================
# Target rules for targets named mongoose_cpp

# Build rule for target.
mongoose_cpp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mongoose_cpp
.PHONY : mongoose_cpp

# fast build rule for target.
mongoose_cpp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_cpp.dir/build.make CMakeFiles/mongoose_cpp.dir/build
.PHONY : mongoose_cpp/fast

#=============================================================================
# Target rules for targets named example

# Build rule for target.
example: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example
.PHONY : example

# fast build rule for target.
example/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example.dir/build.make CMakeFiles/example.dir/build
.PHONY : example/fast

#=============================================================================
# Target rules for targets named test_mongoose_cpp

# Build rule for target.
test_mongoose_cpp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_mongoose_cpp
.PHONY : test_mongoose_cpp

# fast build rule for target.
test_mongoose_cpp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_mongoose_cpp.dir/build.make CMakeFiles/test_mongoose_cpp.dir/build
.PHONY : test_mongoose_cpp/fast

example.o: example.cpp.o
.PHONY : example.o

# target to build an object file
example.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example.dir/build.make CMakeFiles/example.dir/example.cpp.o
.PHONY : example.cpp.o

example.i: example.cpp.i
.PHONY : example.i

# target to preprocess a source file
example.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example.dir/build.make CMakeFiles/example.dir/example.cpp.i
.PHONY : example.cpp.i

example.s: example.cpp.s
.PHONY : example.s

# target to generate assembly for a file
example.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example.dir/build.make CMakeFiles/example.dir/example.cpp.s
.PHONY : example.cpp.s

mongoose.o: mongoose.c.o
.PHONY : mongoose.o

# target to build an object file
mongoose.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose.dir/build.make CMakeFiles/mongoose.dir/mongoose.c.o
.PHONY : mongoose.c.o

mongoose.i: mongoose.c.i
.PHONY : mongoose.i

# target to preprocess a source file
mongoose.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose.dir/build.make CMakeFiles/mongoose.dir/mongoose.c.i
.PHONY : mongoose.c.i

mongoose.s: mongoose.c.s
.PHONY : mongoose.s

# target to generate assembly for a file
mongoose.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose.dir/build.make CMakeFiles/mongoose.dir/mongoose.c.s
.PHONY : mongoose.c.s

mongoose_cpp.o: mongoose_cpp.cpp.o
.PHONY : mongoose_cpp.o

# target to build an object file
mongoose_cpp.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_cpp.dir/build.make CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o
.PHONY : mongoose_cpp.cpp.o

mongoose_cpp.i: mongoose_cpp.cpp.i
.PHONY : mongoose_cpp.i

# target to preprocess a source file
mongoose_cpp.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_cpp.dir/build.make CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.i
.PHONY : mongoose_cpp.cpp.i

mongoose_cpp.s: mongoose_cpp.cpp.s
.PHONY : mongoose_cpp.s

# target to generate assembly for a file
mongoose_cpp.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_cpp.dir/build.make CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.s
.PHONY : mongoose_cpp.cpp.s

test_mongoose_cpp.o: test_mongoose_cpp.cpp.o
.PHONY : test_mongoose_cpp.o

# target to build an object file
test_mongoose_cpp.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_mongoose_cpp.dir/build.make CMakeFiles/test_mongoose_cpp.dir/test_mongoose_cpp.cpp.o
.PHONY : test_mongoose_cpp.cpp.o

test_mongoose_cpp.i: test_mongoose_cpp.cpp.i
.PHONY : test_mongoose_cpp.i

# target to preprocess a source file
test_mongoose_cpp.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_mongoose_cpp.dir/build.make CMakeFiles/test_mongoose_cpp.dir/test_mongoose_cpp.cpp.i
.PHONY : test_mongoose_cpp.cpp.i

test_mongoose_cpp.s: test_mongoose_cpp.cpp.s
.PHONY : test_mongoose_cpp.s

# target to generate assembly for a file
test_mongoose_cpp.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_mongoose_cpp.dir/build.make CMakeFiles/test_mongoose_cpp.dir/test_mongoose_cpp.cpp.s
.PHONY : test_mongoose_cpp.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... example"
	@echo "... mongoose"
	@echo "... mongoose_cpp"
	@echo "... test_mongoose_cpp"
	@echo "... example.o"
	@echo "... example.i"
	@echo "... example.s"
	@echo "... mongoose.o"
	@echo "... mongoose.i"
	@echo "... mongoose.s"
	@echo "... mongoose_cpp.o"
	@echo "... mongoose_cpp.i"
	@echo "... mongoose_cpp.s"
	@echo "... test_mongoose_cpp.o"
	@echo "... test_mongoose_cpp.i"
	@echo "... test_mongoose_cpp.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

