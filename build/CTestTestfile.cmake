# CMake generated Testfile for 
# Source directory: /home/<USER>/onvif
# Build directory: /home/<USER>/onvif/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(mongoose_cpp_test "/home/<USER>/onvif/build/test_mongoose_cpp")
set_tests_properties(mongoose_cpp_test PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/onvif/CMakeLists.txt;137;add_test;/home/<USER>/onvif/CMakeLists.txt;0;")
