#pragma once

#include <memory>
#include <string>
#include <functional>
#include <stdexcept>
#include <vector>
#include <map>
#include <unordered_map>
#include <unordered_set>
#include <chrono>
#include <future>
#include <type_traits>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <regex>
#include <cstring>
#include <cstdarg>

extern "C" {
#include "mongoose.h"
}

namespace mongoose_cpp {

// Forward declarations
class Manager;
class Connection;
class Timer;

// Common types and aliases
using EventHandler = std::function<void(Connection&, int event, void* event_data)>;
using TimerCallback = std::function<void()>;
using TimePoint = std::chrono::steady_clock::time_point;
using Duration = std::chrono::milliseconds;

// Exception classes
class MongooseException : public std::runtime_error {
public:
    explicit MongooseException(const std::string& message) 
        : std::runtime_error("Mongoose Error: " + message) {}
};

class NetworkException : public MongooseException {
public:
    explicit NetworkException(const std::string& message) 
        : MongooseException("Network Error: " + message) {}
};

// String utilities
class StringView {
private:
    const char* data_;
    size_t size_;

public:
    StringView() : data_(nullptr), size_(0) {}
    StringView(const char* str) : data_(str), size_(str ? strlen(str) : 0) {}
    StringView(const char* str, size_t len) : data_(str), size_(len) {}
    StringView(const std::string& str) : data_(str.c_str()), size_(str.size()) {}
    StringView(const struct mg_str& mg_str) : data_(mg_str.buf), size_(mg_str.len) {}

    const char* data() const { return data_; }
    size_t size() const { return size_; }
    bool empty() const { return size_ == 0; }

    std::string to_string() const {
        return data_ ? std::string(data_, size_) : std::string();
    }

    operator std::string() const { return to_string(); }

    struct mg_str to_mg_str() const {
        struct mg_str result;
        result.buf = const_cast<char*>(data_);
        result.len = size_;
        return result;
    }

    bool operator==(const StringView& other) const {
        return size_ == other.size_ && 
               (size_ == 0 || memcmp(data_, other.data_, size_) == 0);
    }

    bool operator!=(const StringView& other) const {
        return !(*this == other);
    }
};

// Buffer management
class Buffer {
private:
    std::vector<char> data_;

public:
    Buffer() = default;
    explicit Buffer(size_t size) : data_(size) {}
    Buffer(const void* data, size_t size) : data_(static_cast<const char*>(data), 
                                                  static_cast<const char*>(data) + size) {}

    char* data() { return data_.data(); }
    const char* data() const { return data_.data(); }
    size_t size() const { return data_.size(); }
    bool empty() const { return data_.empty(); }

    void resize(size_t size) { data_.resize(size); }
    void reserve(size_t size) { data_.reserve(size); }
    void clear() { data_.clear(); }

    void append(const void* data, size_t size) {
        const char* char_data = static_cast<const char*>(data);
        data_.insert(data_.end(), char_data, char_data + size);
    }

    void append(const std::string& str) {
        append(str.data(), str.size());
    }

    std::string to_string() const {
        return std::string(data_.begin(), data_.end());
    }
};

// Address wrapper
class Address {
private:
    struct mg_addr addr_;

public:
    Address() { memset(&addr_, 0, sizeof(addr_)); }
    explicit Address(const struct mg_addr& addr) : addr_(addr) {}

    std::string to_string() const;
    uint16_t port() const;
    bool is_ip4() const { return addr_.is_ip6 == 0; }
    bool is_ip6() const { return addr_.is_ip6 != 0; }

    const struct mg_addr& get_mg_addr() const { return addr_; }
};

// Timer class
class Timer {
private:
    struct mg_timer* timer_;
    unsigned long id_;
    TimerCallback callback_;
    Manager* manager_;
    bool repeat_;

    static void timer_callback(void* arg);

    Timer(Manager* mgr, Duration interval, TimerCallback callback, bool repeat);

public:
    ~Timer();

    Timer(const Timer&) = delete;
    Timer& operator=(const Timer&) = delete;
    Timer(Timer&& other) noexcept;
    Timer& operator=(Timer&& other) noexcept;

    unsigned long get_id() const { return id_; }
    bool is_repeating() const { return repeat_; }
    void cancel();

private:
    void cleanup();
    void move_from(Timer&& other);

    friend class Manager;
};

// RAII wrapper for mg_connection
class Connection : public std::enable_shared_from_this<Connection> {
private:
    struct mg_connection* conn_;
    Manager* manager_;
    EventHandler event_handler_;
    std::atomic<bool> valid_;

    Connection(struct mg_connection* conn, Manager* mgr, EventHandler handler);

public:
    ~Connection();

    Connection(const Connection&) = delete;
    Connection& operator=(const Connection&) = delete;
    Connection(Connection&&) = delete;
    Connection& operator=(Connection&&) = delete;

    static std::shared_ptr<Connection> create(struct mg_connection* conn, Manager* mgr, EventHandler handler);

    // Connection properties
    bool is_valid() const { return valid_.load() && conn_ != nullptr; }
    unsigned long get_id() const { return conn_ ? conn_->id : 0; }
    Address get_local_address() const { return conn_ ? Address(conn_->loc) : Address(); }
    Address get_remote_address() const { return conn_ ? Address(conn_->rem) : Address(); }
    
    // Connection state
    bool is_listening() const { return conn_ && conn_->is_listening; }
    bool is_client() const { return conn_ && conn_->is_client; }
    bool is_accepted() const { return conn_ && conn_->is_accepted; }
    bool is_resolving() const { return conn_ && conn_->is_resolving; }
    bool is_connecting() const { return conn_ && conn_->is_connecting; }
    bool is_tls() const { return conn_ && conn_->is_tls; }
    bool is_tls_handshake() const { return conn_ && conn_->is_tls_hs; }

    // Data transmission
    bool send(const void* data, size_t len) { return conn_ && mg_send(conn_, data, len); }
    bool send(const std::string& data) { return send(data.data(), data.size()); }
    bool send(const Buffer& buffer) { return send(buffer.data(), buffer.size()); }
    
    size_t printf(const char* fmt, ...);
    size_t vprintf(const char* fmt, va_list ap);

    // Buffer access
    Buffer get_recv_buffer() const;
    Buffer get_send_buffer() const;
    void clear_recv_buffer();
    void clear_send_buffer();

    // Connection management
    void close();

    // User data
    void set_user_data(void* data) { if (conn_) conn_->fn_data = data; }
    void* get_user_data() const { return conn_ ? conn_->fn_data : nullptr; }

    // Protocol-specific data
    void set_protocol_data(void* data) { if (conn_) conn_->pfn_data = data; }
    void* get_protocol_data() const { return conn_ ? conn_->pfn_data : nullptr; }

    // TLS
    void* get_tls() const { return conn_ ? conn_->tls : nullptr; }

    // Event handling
    void set_event_handler(EventHandler handler) { event_handler_ = handler; }
    EventHandler get_event_handler() const { return event_handler_; }

    // Internal access
    struct mg_connection* get_mg_connection() { return conn_; }
    const struct mg_connection* get_mg_connection() const { return conn_; }

    void dispatch_event(int event, void* event_data);

private:
    void invalidate();
    friend class Manager;
};

// RAII wrapper for mg_mgr
class Manager {
private:
    struct mg_mgr mgr_;
    std::atomic<bool> running_;
    std::unique_ptr<std::thread> poll_thread_;
    mutable std::mutex connections_mutex_;
    std::unordered_map<unsigned long, std::shared_ptr<Connection>> connections_;
    std::unordered_map<unsigned long, std::shared_ptr<Timer>> timers_;
    mutable std::mutex timers_mutex_;
    std::condition_variable stop_cv_;
    std::mutex stop_mutex_;

    static void internal_event_handler(struct mg_connection* c, int ev, void* ev_data);

public:
    Manager();
    ~Manager();

    Manager(const Manager&) = delete;
    Manager& operator=(const Manager&) = delete;
    Manager(Manager&& other) noexcept;
    Manager& operator=(Manager&& other) noexcept;

    // Core functionality
    void poll(int timeout_ms = 1000);
    void start_polling_thread();
    void stop_polling_thread();
    bool is_running() const { return running_.load(); }

    // Connection management
    std::shared_ptr<Connection> listen(const std::string& url, EventHandler handler);
    std::shared_ptr<Connection> connect(const std::string& url, EventHandler handler);
    std::shared_ptr<Connection> wrap_fd(int fd, EventHandler handler);

    // Timer management
    std::shared_ptr<Timer> add_timer(Duration interval, TimerCallback callback, bool repeat = false);
    void remove_timer(unsigned long timer_id);

    // Configuration
    void set_dns_timeout(int timeout_ms) { mgr_.dnstimeout = timeout_ms; }
    void set_user_data(void* data) { mgr_.userdata = data; }
    void* get_user_data() const { return mgr_.userdata; }
    void set_tls_context(void* tls_ctx) { mgr_.tls_ctx = tls_ctx; }
    void* get_tls_context() const { return mgr_.tls_ctx; }

    // Wakeup functionality
    bool wakeup(unsigned long connection_id, const void* data, size_t len);
    bool init_wakeup();

    // Internal access
    struct mg_mgr* get_mgr() { return &mgr_; }
    const struct mg_mgr* get_mgr() const { return &mgr_; }

    void register_connection(unsigned long id, std::shared_ptr<Connection> conn);
    void unregister_connection(unsigned long id);
    std::shared_ptr<Connection> get_connection(unsigned long id) const;
    void register_timer(unsigned long id, std::shared_ptr<Timer> timer);
    void unregister_timer_internal(unsigned long id);

private:
    void cleanup();
    void move_from(Manager&& other);

    friend class Connection;
};

// HTTP functionality
namespace http {

enum class Method {
    GET, POST, PUT, DELETE, HEAD, OPTIONS, PATCH, CONNECT, TRACE, UNKNOWN
};

enum class Status {
    OK = 200, CREATED = 201, ACCEPTED = 202, NO_CONTENT = 204,
    MOVED_PERMANENTLY = 301, FOUND = 302, NOT_MODIFIED = 304,
    BAD_REQUEST = 400, UNAUTHORIZED = 401, FORBIDDEN = 403, NOT_FOUND = 404,
    METHOD_NOT_ALLOWED = 405, CONFLICT = 409,
    INTERNAL_SERVER_ERROR = 500, NOT_IMPLEMENTED = 501, BAD_GATEWAY = 502,
    SERVICE_UNAVAILABLE = 503
};

class Header {
private:
    std::string name_, value_;
public:
    Header() = default;
    Header(const std::string& name, const std::string& value) : name_(name), value_(value) {}
    Header(const struct mg_http_header& header)
        : name_(header.name.buf, header.name.len), value_(header.value.buf, header.value.len) {}

    const std::string& name() const { return name_; }
    const std::string& value() const { return value_; }
    void set_name(const std::string& name) { name_ = name; }
    void set_value(const std::string& value) { value_ = value; }
};

class Message {
private:
    struct mg_http_message* msg_;
    std::vector<Header> headers_;
    bool headers_parsed_;
    void parse_headers();

public:
    explicit Message(struct mg_http_message* msg);

    StringView method() const { return StringView(msg_->method.buf, msg_->method.len); }
    StringView uri() const { return StringView(msg_->uri.buf, msg_->uri.len); }
    StringView query() const { return StringView(msg_->query.buf, msg_->query.len); }
    StringView proto() const { return StringView(msg_->proto.buf, msg_->proto.len); }
    StringView body() const { return StringView(msg_->body.buf, msg_->body.len); }
    StringView head() const { return StringView(msg_->head.buf, msg_->head.len); }
    StringView message() const { return StringView(msg_->message.buf, msg_->message.len); }

    const std::vector<Header>& headers();
    std::string get_header(const std::string& name) const;
    bool has_header(const std::string& name) const;
    std::string get_query_param(const std::string& name) const;
    std::map<std::string, std::string> get_query_params() const;
    std::string get_form_param(const std::string& name) const;
    std::map<std::string, std::string> get_form_params() const;

    Method get_method() const;
    std::string get_path() const;
    bool is_websocket_upgrade() const;
    std::pair<std::string, std::string> get_basic_auth() const;
    std::string get_content_type() const;
    size_t get_content_length() const;

    struct mg_http_message* get_mg_message() { return msg_; }
    const struct mg_http_message* get_mg_message() const { return msg_; }
    struct mg_http_message* get_mg_message_mutable() const { return msg_; }
};

class Response {
private:
    Connection& conn_;
    Status status_;
    std::map<std::string, std::string> headers_;
    std::string body_;
    bool sent_;

public:
    explicit Response(Connection& conn) : conn_(conn), status_(Status::OK), sent_(false) {}
    ~Response();

    Response(const Response&) = delete;
    Response& operator=(const Response&) = delete;
    Response(Response&& other) noexcept;
    Response& operator=(Response&& other) noexcept;

    Response& status(Status status) { status_ = status; return *this; }
    Response& status(int status) { status_ = static_cast<Status>(status); return *this; }
    Response& header(const std::string& name, const std::string& value);
    Response& content_type(const std::string& type);
    Response& content_length(size_t length);
    Response& body(const std::string& body) { body_ = body; return *this; }
    Response& body(const char* body) { body_ = body ? body : ""; return *this; }
    Response& json(const std::string& json);
    Response& html(const std::string& html);
    Response& text(const std::string& text);

    void send();
    void send(const std::string& body);
    void send(Status status, const std::string& body);
    void send_chunk(const std::string& chunk);
    void end_chunks();
    void send_file(const std::string& path);
    void send_file(const std::string& path, const std::string& content_type);
    void redirect(const std::string& url, Status status = Status::FOUND);
    void error(Status status, const std::string& message = "");

private:
    void ensure_not_sent();
    std::string build_headers();

    friend class Server;
};

using RouteHandler = std::function<void(const Message&, Response&)>;

class Route {
private:
    std::string pattern_;
    std::regex regex_;
    RouteHandler handler_;
    Method method_;

public:
    Route(Method method, const std::string& pattern, RouteHandler handler);
    bool matches(Method method, const std::string& path) const;
    void handle(const Message& request, Response& response) const;
    Method get_method() const { return method_; }
    const std::string& get_pattern() const { return pattern_; }

private:
    std::regex compile_pattern(const std::string& pattern);
};

class Server {
private:
    Manager& manager_;
    std::shared_ptr<Connection> listener_;
    std::vector<Route> routes_;
    std::string document_root_;
    bool serve_static_;

    static void http_event_handler(Connection& conn, int event, void* event_data);
    void handle_http_message(Connection& conn, struct mg_http_message* msg);
    bool handle_route(const Message& request, Response& response);
    void handle_static_file(const Message& request, Response& response);

public:
    explicit Server(Manager& manager);
    ~Server() = default;

    Server(const Server&) = delete;
    Server& operator=(const Server&) = delete;
    Server(Server&& other) noexcept = default;
    Server& operator=(Server&& other) noexcept = default;

    void listen(const std::string& address);
    void stop();
    bool is_listening() const;

    Server& get(const std::string& pattern, RouteHandler handler);
    Server& post(const std::string& pattern, RouteHandler handler);
    Server& put(const std::string& pattern, RouteHandler handler);
    Server& delete_(const std::string& pattern, RouteHandler handler);
    Server& head(const std::string& pattern, RouteHandler handler);
    Server& options(const std::string& pattern, RouteHandler handler);
    Server& patch(const std::string& pattern, RouteHandler handler);
    Server& route(Method method, const std::string& pattern, RouteHandler handler);

    Server& static_files(const std::string& document_root);
    Server& disable_static_files();
};

std::string method_to_string(Method method);
Method string_to_method(const std::string& method);
std::string status_to_string(Status status);
const char* status_to_reason_phrase(Status status);

} // namespace http

// WebSocket functionality
namespace websocket {

enum class MessageType {
    TEXT = WEBSOCKET_OP_TEXT,
    BINARY = WEBSOCKET_OP_BINARY,
    CLOSE = WEBSOCKET_OP_CLOSE,
    PING = WEBSOCKET_OP_PING,
    PONG = WEBSOCKET_OP_PONG,
    CONTINUE = WEBSOCKET_OP_CONTINUE
};

class Message {
private:
    struct mg_ws_message* msg_;

public:
    explicit Message(struct mg_ws_message* msg) : msg_(msg) {}

    StringView data() const { return StringView(msg_->data.buf, msg_->data.len); }
    MessageType type() const { return static_cast<MessageType>(msg_->flags & 0x0F); }
    bool is_text() const { return type() == MessageType::TEXT; }
    bool is_binary() const { return type() == MessageType::BINARY; }
    bool is_close() const { return type() == MessageType::CLOSE; }
    bool is_ping() const { return type() == MessageType::PING; }
    bool is_pong() const { return type() == MessageType::PONG; }

    std::string to_string() const { return data().to_string(); }
    Buffer to_buffer() const {
        auto d = data();
        return Buffer(d.data(), d.size());
    }

    struct mg_ws_message* get_mg_message() { return msg_; }
    const struct mg_ws_message* get_mg_message() const { return msg_; }
};

class WebSocketConnection {
private:
    std::shared_ptr<Connection> conn_;

public:
    explicit WebSocketConnection(std::shared_ptr<Connection> conn) : conn_(conn) {}

    bool is_valid() const { return conn_ && conn_->is_valid(); }
    unsigned long get_id() const { return conn_ ? conn_->get_id() : 0; }
    Address get_remote_address() const { return conn_ ? conn_->get_remote_address() : Address(); }

    bool send_text(const std::string& text);
    bool send_binary(const void* data, size_t len);
    bool send_binary(const Buffer& buffer);
    bool send_ping(const std::string& data = "");
    bool send_pong(const std::string& data = "");
    bool send_close(uint16_t code = 1000, const std::string& reason = "");

    size_t printf(const char* fmt, ...);
    size_t vprintf(const char* fmt, va_list ap);

    void close() { if (conn_) conn_->close(); }

    void set_user_data(void* data) { if (conn_) conn_->set_user_data(data); }
    void* get_user_data() const { return conn_ ? conn_->get_user_data() : nullptr; }

    std::shared_ptr<Connection> get_connection() { return conn_; }
    std::shared_ptr<const Connection> get_connection() const { return conn_; }
};

using OnConnectHandler = std::function<void(WebSocketConnection&)>;
using OnMessageHandler = std::function<void(WebSocketConnection&, const Message&)>;
using OnCloseHandler = std::function<void(WebSocketConnection&)>;
using OnErrorHandler = std::function<void(WebSocketConnection&, const std::string&)>;

class Server {
private:
    Manager& manager_;
    std::shared_ptr<Connection> listener_;
    std::unordered_set<unsigned long> connections_;
    mutable std::mutex connections_mutex_;

    OnConnectHandler on_connect_;
    OnMessageHandler on_message_;
    OnCloseHandler on_close_;
    OnErrorHandler on_error_;

    std::string upgrade_path_;

    static void websocket_event_handler(Connection& conn, int event, void* event_data);
    void handle_http_message(Connection& conn, struct mg_http_message* msg);
    void handle_websocket_open(Connection& conn, struct mg_http_message* msg);
    void handle_websocket_message(Connection& conn, struct mg_ws_message* msg);
    void handle_websocket_close(Connection& conn);

public:
    explicit Server(Manager& manager);
    ~Server();

    Server(const Server&) = delete;
    Server& operator=(const Server&) = delete;
    Server(Server&& other) noexcept;
    Server& operator=(Server&& other) noexcept;

    void listen(const std::string& address, const std::string& path = "/ws");
    void stop();
    bool is_listening() const;

    Server& on_connect(OnConnectHandler handler) { on_connect_ = handler; return *this; }
    Server& on_message(OnMessageHandler handler) { on_message_ = handler; return *this; }
    Server& on_close(OnCloseHandler handler) { on_close_ = handler; return *this; }
    Server& on_error(OnErrorHandler handler) { on_error_ = handler; return *this; }

    std::vector<WebSocketConnection> get_connections() const;
    size_t get_connection_count() const;
    void broadcast_text(const std::string& text);
    void broadcast_binary(const void* data, size_t len);
    void broadcast_binary(const Buffer& buffer);
    void close_all_connections();

private:
    void add_connection(unsigned long id);
    void remove_connection(unsigned long id);
    void move_from(Server&& other);
};

class Client {
private:
    Manager& manager_;
    std::shared_ptr<Connection> conn_;

    OnConnectHandler on_connect_;
    OnMessageHandler on_message_;
    OnCloseHandler on_close_;
    OnErrorHandler on_error_;

    static void client_event_handler(Connection& conn, int event, void* event_data);
    void handle_websocket_open(Connection& conn, struct mg_http_message* msg);
    void handle_websocket_message(Connection& conn, struct mg_ws_message* msg);
    void handle_websocket_close(Connection& conn);

public:
    explicit Client(Manager& manager);
    ~Client() = default;

    Client(const Client&) = delete;
    Client& operator=(const Client&) = delete;
    Client(Client&& other) noexcept = default;
    Client& operator=(Client&& other) noexcept = default;

    void connect(const std::string& url);
    void disconnect();
    bool is_connected() const;

    Client& on_connect(OnConnectHandler handler) { on_connect_ = handler; return *this; }
    Client& on_message(OnMessageHandler handler) { on_message_ = handler; return *this; }
    Client& on_close(OnCloseHandler handler) { on_close_ = handler; return *this; }
    Client& on_error(OnErrorHandler handler) { on_error_ = handler; return *this; }

    bool send_text(const std::string& text);
    bool send_binary(const void* data, size_t len);
    bool send_binary(const Buffer& buffer);
    bool send_ping(const std::string& data = "");
    bool send_pong(const std::string& data = "");
    bool send_close(uint16_t code = 1000, const std::string& reason = "");

    size_t printf(const char* fmt, ...);
    size_t vprintf(const char* fmt, va_list ap);

    unsigned long get_connection_id() const;
    Address get_remote_address() const;

    void set_user_data(void* data);
    void* get_user_data() const;
};

std::string message_type_to_string(MessageType type);
MessageType string_to_message_type(const std::string& type);

} // namespace websocket

// MQTT functionality
namespace mqtt {

enum class QoS : uint8_t {
    AT_MOST_ONCE = 0,
    AT_LEAST_ONCE = 1,
    EXACTLY_ONCE = 2
};

class Options {
public:
    std::string user;
    std::string password;
    std::string client_id;
    std::string topic;
    std::string message;
    QoS qos = QoS::AT_MOST_ONCE;
    uint8_t version = 4; // MQTT 3.1.1
    uint16_t keepalive = 60;
    uint16_t retransmit_id = 0;
    bool retain = false;
    bool clean = true;

    struct mg_mqtt_opts to_mg_opts() const;
};

class Message {
private:
    struct mg_mqtt_message* msg_;

public:
    explicit Message(struct mg_mqtt_message* msg) : msg_(msg) {}

    StringView topic() const { return StringView(msg_->topic.buf, msg_->topic.len); }
    StringView data() const { return StringView(msg_->data.buf, msg_->data.len); }
    StringView dgram() const { return StringView(msg_->dgram.buf, msg_->dgram.len); }
    uint16_t id() const { return msg_->id; }
    uint8_t cmd() const { return msg_->cmd; }
    QoS qos() const { return static_cast<QoS>(msg_->qos); }
    uint8_t ack() const { return msg_->ack; }

    std::string topic_string() const { return topic().to_string(); }
    std::string data_string() const { return data().to_string(); }
    Buffer data_buffer() const {
        auto d = data();
        return Buffer(d.data(), d.size());
    }

    struct mg_mqtt_message* get_mg_message() { return msg_; }
    const struct mg_mqtt_message* get_mg_message() const { return msg_; }
};

using OnConnectHandler = std::function<void()>;
using OnMessageHandler = std::function<void(const Message&)>;
using OnDisconnectHandler = std::function<void()>;
using OnErrorHandler = std::function<void(const std::string&)>;

class Client {
private:
    Manager& manager_;
    std::shared_ptr<Connection> conn_;
    Options options_;

    OnConnectHandler on_connect_;
    OnMessageHandler on_message_;
    OnDisconnectHandler on_disconnect_;
    OnErrorHandler on_error_;

    static void mqtt_event_handler(Connection& conn, int event, void* event_data);
    void handle_mqtt_open(Connection& conn, int* connack_status);
    void handle_mqtt_message(Connection& conn, struct mg_mqtt_message* msg);
    void handle_mqtt_close(Connection& conn);

public:
    explicit Client(Manager& manager);
    ~Client() = default;

    Client(const Client&) = delete;
    Client& operator=(const Client&) = delete;
    Client(Client&& other) noexcept = default;
    Client& operator=(Client&& other) noexcept = default;

    void connect(const std::string& url, const Options& options);
    void disconnect();
    bool is_connected() const;

    Client& on_connect(OnConnectHandler handler) { on_connect_ = handler; return *this; }
    Client& on_message(OnMessageHandler handler) { on_message_ = handler; return *this; }
    Client& on_disconnect(OnDisconnectHandler handler) { on_disconnect_ = handler; return *this; }
    Client& on_error(OnErrorHandler handler) { on_error_ = handler; return *this; }

    uint16_t publish(const std::string& topic, const std::string& message,
                     QoS qos = QoS::AT_MOST_ONCE, bool retain = false);
    uint16_t publish(const Options& options);

    void subscribe(const std::string& topic, QoS qos = QoS::AT_MOST_ONCE);
    void unsubscribe(const std::string& topic);

    void ping();
    void pong();

    unsigned long get_connection_id() const;
    Address get_remote_address() const;

    void set_user_data(void* data);
    void* get_user_data() const;

private:
    Options create_publish_options(const std::string& topic, const std::string& message,
                                   QoS qos, bool retain);
    Options create_subscribe_options(const std::string& topic, QoS qos);
};

std::string qos_to_string(QoS qos);
QoS string_to_qos(const std::string& qos);

} // namespace mqtt

// File serving functionality
namespace file {

struct ServeOptions {
    std::string root_dir;
    std::string ssi_pattern;
    std::string extra_headers;
    std::string mime_types;
    std::string page404;

    struct mg_http_serve_opts to_mg_opts() const;
};

class FileServer {
private:
    Manager& manager_;
    std::shared_ptr<Connection> listener_;
    ServeOptions options_;

    static void file_event_handler(Connection& conn, int event, void* event_data);
    void handle_http_message(Connection& conn, struct mg_http_message* msg);

public:
    explicit FileServer(Manager& manager);
    ~FileServer() = default;

    FileServer(const FileServer&) = delete;
    FileServer& operator=(const FileServer&) = delete;
    FileServer(FileServer&& other) noexcept = default;
    FileServer& operator=(FileServer&& other) noexcept = default;

    void listen(const std::string& address, const ServeOptions& options);
    void stop();
    bool is_listening() const;

    void set_options(const ServeOptions& options) { options_ = options; }
    const ServeOptions& get_options() const { return options_; }
};

} // namespace file

// Utility functions
std::string url_encode(const std::string& str);
std::string url_decode(const std::string& str);
std::string base64_encode(const std::string& str);
std::string base64_decode(const std::string& str);

} // namespace mongoose_cpp
