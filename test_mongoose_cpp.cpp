#include "mongoose_cpp.hpp"
#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>

using namespace mongoose_cpp;

// Simple test framework
class TestRunner {
private:
    int tests_run = 0;
    int tests_passed = 0;
    
public:
    void run_test(const std::string& name, std::function<void()> test) {
        std::cout << "Running test: " << name << " ... ";
        tests_run++;
        
        try {
            test();
            tests_passed++;
            std::cout << "PASSED" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "FAILED: " << e.what() << std::endl;
        } catch (...) {
            std::cout << "FAILED: Unknown exception" << std::endl;
        }
    }
    
    void print_summary() {
        std::cout << "\nTest Summary: " << tests_passed << "/" << tests_run << " tests passed" << std::endl;
        if (tests_passed == tests_run) {
            std::cout << "All tests PASSED!" << std::endl;
        } else {
            std::cout << (tests_run - tests_passed) << " tests FAILED!" << std::endl;
        }
    }
    
    bool all_passed() const {
        return tests_passed == tests_run;
    }
};

// Test StringView
void test_string_view() {
    StringView sv1("hello");
    assert(sv1.size() == 5);
    assert(sv1.to_string() == "hello");
    
    StringView sv2("world", 3);
    assert(sv2.size() == 3);
    assert(sv2.to_string() == "wor");
    
    StringView sv3;
    assert(sv3.empty());
    assert(sv3.size() == 0);
    
    std::string str = "test";
    StringView sv4(str);
    assert(sv4.size() == 4);
    assert(sv4.to_string() == "test");
}

// Test Buffer
void test_buffer() {
    Buffer buf1;
    assert(buf1.empty());
    assert(buf1.size() == 0);
    
    Buffer buf2(10);
    assert(buf2.size() == 10);
    assert(!buf2.empty());
    
    const char* data = "hello";
    Buffer buf3(data, 5);
    assert(buf3.size() == 5);
    assert(buf3.to_string() == "hello");
    
    buf3.append(" world", 6);
    assert(buf3.to_string() == "hello world");
    
    buf3.clear();
    assert(buf3.empty());
}

// Test Manager
void test_manager() {
    Manager mgr;
    assert(!mgr.is_running());

    // Just test basic functionality without timers
    // (Timer functionality requires more complex setup)
}

// Test HTTP utilities
void test_http_utilities() {
    using namespace http;
    
    // Test method conversion
    assert(method_to_string(Method::GET) == "GET");
    assert(method_to_string(Method::POST) == "POST");
    assert(string_to_method("GET") == Method::GET);
    assert(string_to_method("POST") == Method::POST);
    assert(string_to_method("INVALID") == Method::UNKNOWN);
    
    // Test status conversion
    assert(status_to_string(Status::OK) == "200");
    assert(status_to_string(Status::NOT_FOUND) == "404");
    assert(std::string(status_to_reason_phrase(Status::OK)) == "OK");
    assert(std::string(status_to_reason_phrase(Status::NOT_FOUND)) == "Not Found");
}

// Test WebSocket utilities
void test_websocket_utilities() {
    using namespace websocket;
    
    assert(message_type_to_string(MessageType::TEXT) == "text");
    assert(message_type_to_string(MessageType::BINARY) == "binary");
    assert(string_to_message_type("text") == MessageType::TEXT);
    assert(string_to_message_type("binary") == MessageType::BINARY);
}

// Test MQTT utilities
void test_mqtt_utilities() {
    using namespace mqtt;
    
    assert(qos_to_string(QoS::AT_MOST_ONCE) == "0");
    assert(qos_to_string(QoS::AT_LEAST_ONCE) == "1");
    assert(qos_to_string(QoS::EXACTLY_ONCE) == "2");
    assert(string_to_qos("0") == QoS::AT_MOST_ONCE);
    assert(string_to_qos("1") == QoS::AT_LEAST_ONCE);
    assert(string_to_qos("2") == QoS::EXACTLY_ONCE);
}

// Test URL encoding/decoding
void test_url_encoding() {
    std::string original = "hello world!@#$%";
    std::string encoded = url_encode(original);
    std::string decoded = url_decode(encoded);
    
    assert(!encoded.empty());
    assert(decoded == original);
    
    // Test specific cases
    assert(url_encode("hello world") == "hello%20world");
    assert(url_decode("hello%20world") == "hello world");
    assert(url_decode("hello+world") == "hello world");
}

// Test Base64 encoding/decoding
void test_base64_encoding() {
    std::string original = "hello world";
    std::string encoded = base64_encode(original);
    std::string decoded = base64_decode(encoded);
    
    assert(!encoded.empty());
    assert(decoded == original);
    
    // Test empty string
    assert(base64_encode("") == "");
    assert(base64_decode("") == "");
}

// Test HTTP Server (basic functionality)
void test_http_server() {
    Manager mgr;
    http::Server server(mgr);
    
    bool route_called = false;
    server.get("/test", [&route_called](const http::Message& req, http::Response& res) {
        route_called = true;
        res.text("test response");
    });
    
    // We can't easily test the actual HTTP functionality without a real connection,
    // but we can test that the server can be created and configured
    assert(!server.is_listening());
    
    // Test route registration
    server.post("/api", [](const http::Message& req, http::Response& res) {
        res.json("{\"status\": \"ok\"}");
    });
    
    server.static_files("./www");
    server.disable_static_files();
}

// Test WebSocket Server (basic functionality)
void test_websocket_server() {
    Manager mgr;
    websocket::Server server(mgr);
    
    bool connect_called = false;
    bool message_called = false;
    bool close_called = false;
    
    server.on_connect([&connect_called](websocket::WebSocketConnection& conn) {
        connect_called = true;
    });
    
    server.on_message([&message_called](websocket::WebSocketConnection& conn, const websocket::Message& msg) {
        message_called = true;
    });
    
    server.on_close([&close_called](websocket::WebSocketConnection& conn) {
        close_called = true;
    });
    
    assert(!server.is_listening());
    assert(server.get_connection_count() == 0);
}

// Test MQTT Client (basic functionality)
void test_mqtt_client() {
    Manager mgr;
    mqtt::Client client(mgr);
    
    bool connect_called = false;
    bool message_called = false;
    bool disconnect_called = false;
    
    client.on_connect([&connect_called]() {
        connect_called = true;
    });
    
    client.on_message([&message_called](const mqtt::Message& msg) {
        message_called = true;
    });
    
    client.on_disconnect([&disconnect_called]() {
        disconnect_called = true;
    });
    
    assert(!client.is_connected());
    
    // Test options
    mqtt::Options options;
    options.client_id = "test_client";
    options.keepalive = 30;
    options.qos = mqtt::QoS::AT_LEAST_ONCE;
    
    assert(options.client_id == "test_client");
    assert(options.keepalive == 30);
    assert(options.qos == mqtt::QoS::AT_LEAST_ONCE);
}

int main() {
    std::cout << "Mongoose C++ Test Suite" << std::endl;
    std::cout << "=======================" << std::endl;
    
    TestRunner runner;
    
    runner.run_test("StringView", test_string_view);
    runner.run_test("Buffer", test_buffer);
    runner.run_test("Manager", test_manager);
    runner.run_test("HTTP Utilities", test_http_utilities);
    runner.run_test("WebSocket Utilities", test_websocket_utilities);
    runner.run_test("MQTT Utilities", test_mqtt_utilities);
    runner.run_test("URL Encoding", test_url_encoding);
    runner.run_test("Base64 Encoding", test_base64_encoding);
    runner.run_test("HTTP Server", test_http_server);
    runner.run_test("WebSocket Server", test_websocket_server);
    runner.run_test("MQTT Client", test_mqtt_client);
    
    runner.print_summary();
    
    return runner.all_passed() ? 0 : 1;
}
